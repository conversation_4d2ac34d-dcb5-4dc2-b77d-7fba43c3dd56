{"name": "is-data-view", "version": "1.0.2", "description": "Is this value a JS DataView? This module works cross-realm/iframe, does not depend on `instanceof` or mutable properties, and despite ES6 Symbol.toStringTag.", "main": "index.js", "exports": {".": "./index.js", "./package.json": "./package.json"}, "types": "./index.d.ts", "scripts": {"prepack": "npmignore --auto --commentLines=autogenerated", "prepublishOnly": "safe-publish-latest", "prepublish": "not-in-publish || npm run prepublishOnly", "prelint": "evalmd README.md", "lint": "eslint --ext=js,mjs .", "postlint": "tsc -p .", "pretest": "npm run lint", "tests-only": "nyc tape 'test/**/*.js'", "test": "npm run tests-only", "posttest": "npx npm@'>= 10.2' audit --production", "version": "auto-changelog && git add CHANGELOG.md", "postversion": "auto-changelog && git add CHANGELOG.md && git commit --no-edit --amend && git tag -f \"v$(node -e \"console.log(require('./package.json').version)\")\""}, "funding": {"url": "https://github.com/sponsors/ljharb"}, "repository": {"type": "git", "url": "git+https://github.com/inspect-js/is-data-view.git"}, "keywords": ["javascript", "ecmascript", "dataview", "data", "view", "typedarray", "typedarrays"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/inspect-js/is-data-view/issues"}, "homepage": "https://github.com/inspect-js/is-data-view#readme", "dependencies": {"call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "is-typed-array": "^1.1.13"}, "devDependencies": {"@ljharb/eslint-config": "^21.1.1", "@ljharb/tsconfig": "^0.2.2", "@types/call-bind": "^1.0.5", "@types/es-value-fixtures": "^1.4.4", "@types/for-each": "^0.3.3", "@types/make-arrow-function": "^1.2.2", "@types/make-generator-function": "^2.0.3", "@types/node": "^20.17.10", "@types/object-inspect": "^1.13.0", "@types/tape": "^5.6.5", "auto-changelog": "^2.5.0", "available-typed-arrays": "^1.0.7", "encoding": "^0.1.13", "es-value-fixtures": "^1.5.0", "eslint": "=8.8.0", "evalmd": "^0.0.19", "for-each": "^0.3.3", "has-tostringtag": "^1.0.2", "in-publish": "^2.0.1", "make-arrow-function": "^1.2.0", "make-generator-function": "^2.0.0", "npmignore": "^0.3.1", "nyc": "^10.3.2", "object-inspect": "^1.13.3", "safe-publish-latest": "^2.0.0", "tape": "^5.9.0", "typescript": "next"}, "testling": {"files": "test/index.js"}, "engines": {"node": ">= 0.4"}, "auto-changelog": {"output": "CHANGELOG.md", "template": "<PERSON><PERSON><PERSON><PERSON>", "unreleased": false, "commitLimit": false, "backfillLimit": false, "hideCredit": true}, "publishConfig": {"ignore": [".github/workflows"]}}