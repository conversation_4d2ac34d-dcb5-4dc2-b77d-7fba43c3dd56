{"version": 3, "file": "typanion.js", "sources": ["../src/typanion.ts"], "sourcesContent": ["import { toNestErrors, validateFieldsNatively } from '@hookform/resolvers';\nimport type {\n  FieldError,\n  FieldErrors,\n  FieldValues,\n  Resolver,\n} from 'react-hook-form';\nimport * as t from 'typanion';\n\nfunction parseErrors(errors: string[], parsedErrors: FieldErrors = {}) {\n  return errors.reduce((acc, error) => {\n    const fieldIndex = error.indexOf(':');\n\n    const field = error.slice(1, fieldIndex);\n    const message = error.slice(fieldIndex + 1).trim();\n\n    acc[field] = {\n      message,\n    } as FieldError;\n\n    return acc;\n  }, parsedErrors);\n}\n\nexport function typanionResolver<Input extends FieldValues, Context, Output>(\n  schema: t.StrictValidator<Input, Input>,\n  schemaOptions?: Pick<t.ValidationState, 'coercions' | 'coercion'>,\n  resolverOptions?: {\n    mode?: 'async' | 'sync';\n    raw?: false;\n  },\n): Resolver<Input, Context, t.InferType<typeof schema>>;\n\nexport function typanionResolver<Input extends FieldValues, Context, Output>(\n  schema: t.StrictValidator<Input, Input>,\n  schemaOptions: Pick<t.ValidationState, 'coercions' | 'coercion'> | undefined,\n  resolverOptions: {\n    mode?: 'async' | 'sync';\n    raw: true;\n  },\n): Resolver<Input, Context, Input>;\n\n/**\n * Creates a resolver for react-hook-form using Typanion schema validation\n * @param {t.StrictValidator<TFieldValues, TFieldValues>} schema - The Typanion schema to validate against\n * @param {Pick<t.ValidationState, 'coercions' | 'coercion'>} [schemaOptions] - Optional Typanion validation options\n * @returns {Resolver<t.InferType<typeof schema>>} A resolver function compatible with react-hook-form\n * @example\n * const schema = t.isObject({\n *   name: t.isString(),\n *   age: t.isInteger()\n * });\n *\n * useForm({\n *   resolver: typanionResolver(schema)\n * });\n */\nexport function typanionResolver<Input extends FieldValues, Context, Output>(\n  schema: t.StrictValidator<Input, Input>,\n  schemaOptions: Pick<t.ValidationState, 'coercions' | 'coercion'> = {},\n): Resolver<Input, Context, Output | Input> {\n  return (values: Input, _, options) => {\n    const rawErrors: string[] = [];\n    const isValid = schema(\n      values,\n      Object.assign(\n        {},\n        {\n          errors: rawErrors,\n        },\n        schemaOptions,\n      ),\n    );\n    const parsedErrors = parseErrors(rawErrors);\n\n    if (isValid) {\n      options.shouldUseNativeValidation &&\n        validateFieldsNatively(parsedErrors, options);\n\n      return { values, errors: {} };\n    }\n\n    return { values: {}, errors: toNestErrors(parsedErrors, options) };\n  };\n}\n"], "names": ["schema", "schemaOptions", "values", "_", "options", "rawErrors", "<PERSON><PERSON><PERSON><PERSON>", "Object", "assign", "errors", "parsedErrors", "reduce", "acc", "error", "fieldIndex", "indexOf", "field", "slice", "message", "trim", "parseErrors", "shouldUseNativeValidation", "validateFieldsNatively", "toNestErrors"], "mappings": "8DAyDgB,SACdA,EACAC,GAEA,YAFAA,IAAAA,IAAAA,EAAmE,CAAE,GAE7DC,SAAAA,EAAeC,EAAGC,GACxB,IAAMC,EAAsB,GACtBC,EAAUN,EACdE,EACAK,OAAOC,OACL,CAAE,EACF,CACEC,OAAQJ,GAEVJ,IAGES,EAhEV,SAAqBD,EAAkBC,GACrC,gBADqCA,IAAAA,EAA4B,IAC1DD,EAAOE,OAAO,SAACC,EAAKC,GACzB,IAAMC,EAAaD,EAAME,QAAQ,KAE3BC,EAAQH,EAAMI,MAAM,EAAGH,GACvBI,EAAUL,EAAMI,MAAMH,EAAa,GAAGK,OAM5C,OAJAP,EAAII,GAAS,CACXE,QAAAA,GAGKN,CACT,EAAGF,EACL,CAmDyBU,CAAYf,GAEjC,OAAIC,GACFF,EAAQiB,2BACNC,EAAAA,uBAAuBZ,EAAcN,GAEhC,CAAEF,OAAAA,EAAQO,OAAQ,CAAA,IAGpB,CAAEP,OAAQ,GAAIO,OAAQc,EAAYA,aAACb,EAAcN,GAC1D,CACF"}