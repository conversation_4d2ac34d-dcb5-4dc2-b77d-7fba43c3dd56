{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\r\nimport { twMerge } from \"tailwind-merge\"\r\n\r\nexport function cn(...inputs: ClassValue[]) {\r\n  return twMerge(clsx(inputs))\r\n}\r\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\r\n        ghost:\r\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\r\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\r\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\r\n        icon: \"size-9\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Button({\r\n  className,\r\n  variant,\r\n  size,\r\n  asChild = false,\r\n  ...props\r\n}: React.ComponentProps<\"button\"> &\r\n  VariantProps<typeof buttonVariants> & {\r\n    asChild?: boolean\r\n  }) {\r\n  const Comp = asChild ? Slot : \"button\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"button\"\r\n      className={cn(buttonVariants({ variant, size, className }))}\r\n      {...props}\r\n    />\r\n  )\r\n}\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 80, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewInstructions.tsx"], "sourcesContent": ["\"use client\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport { ArrowRight } from \"lucide-react\";\r\nimport React, { useState } from \"react\";\r\n\r\ntype InterviewInstructionsProps = {\r\n  candidateName?: string;\r\n  jobTitle?: string;\r\n  languages?: string[];\r\n  instructions?: string[];\r\n  environmentChecklist?: string[];\r\n  disclaimers?: string[];\r\n  onNext?: () => void;\r\n};\r\n\r\nconst defaultInstructions = [\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n  \"The AI Video assessment consists of 5 questions and should take you 5 minutes to complete, depending on the number of questions you are assigned.\",\r\n];\r\n\r\nconst defaultEnvironment = [\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n  \"To ensure an accurate assessment, please sit in a well lit and strong wifi area. Make sure your face is clearly visible, and avoid sitting with strong backlighting or in places where shadows may obscure your face.\",\r\n];\r\n\r\nconst defaultDisclaimers = [\r\n  \"Environment Requirements Ensure you are in a quiet, distraction-free space. Sit in a well-lit area so the avatar can see you clearly. Use a stable internet connection and a working camera & microphone .\",\r\n  \"AI Interview Format Your interviewer will be an AI avatar, speaking and listening in a natural, conversational style. You will respond to 5 preset questions, with roughly under 10 minutes total interview time. You may be gently prompted if your answers run long—please stay within the time suggested .\",\r\n  \"Recording & Usage This session will be fully recorded (audio & video) for review by our hiring team. Your responses and the recording will be processed by our AI scoring system to evaluate communication, problem-solving, and fit. All data is stored securely and used only for the purposes of hiring this role .\",\r\n  \"Independence & Integrity Please answer without external aids (notes, websites, or other people). If background noise or interruptions occur, you may be prompted to pause and restart your answer .\",\r\n];\r\n\r\nconst InterviewInstructions: React.FC<InterviewInstructionsProps> = ({\r\n  candidateName = \"Jonathan\",\r\n  jobTitle = \"Insurance Agent\",\r\n  languages = [\"English\", \"Chinese\"],\r\n  instructions = defaultInstructions,\r\n  environmentChecklist = defaultEnvironment,\r\n  disclaimers = defaultDisclaimers,\r\n  onNext,\r\n}) => {\r\n  const [isChecked, setIsChecked] = useState(false);\r\n\r\n  return (\r\n    <div className=\"flex-1 border border-gray-400 rounded-md h-fit bg-white\">\r\n      <div className=\"p-4 flex flex-col text-[#38383a]\">\r\n        <p className=\"font-semibold mb-8 text-xl\">\r\n          Instructions for Interview!\r\n        </p>\r\n        <div className=\"space-y-6\">\r\n          <div>\r\n            <p className=\" mb-2 text-md\">Hello {candidateName}!</p>\r\n            <p className=\"text-sm mb-4\">\r\n              As part of the process you are required to complete an AI video\r\n              assessment for the role of the {jobTitle}.\r\n            </p>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Interview Language</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {languages.map((language, index) => (\r\n                <li key={index}>{language}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Instructions</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {instructions.map((instruction, index) => (\r\n                <li key={index}>{instruction}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Environment Checklist:</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {environmentChecklist.map((item, index) => (\r\n                <li key={index}>{item}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div>\r\n            <p className=\"font-semibold mb-2 text-lg\">Important Disclaimers:</p>\r\n            <ul className=\"list-disc list-inside space-y-2 text-sm\">\r\n              {disclaimers.map((disclaimer, index) => (\r\n                <li key={index}>{disclaimer}</li>\r\n              ))}\r\n            </ul>\r\n          </div>\r\n\r\n          <div className=\"flex items-start gap-2 mt-6\">\r\n            <input\r\n              type=\"checkbox\"\r\n              id=\"terms\"\r\n              checked={isChecked}\r\n              onChange={(e) => setIsChecked(e.target.checked)}\r\n              className=\"h-4 w-4 text-blue-600 border-gray-300 rounded focus:ring-blue-500\"\r\n            />\r\n            <label htmlFor=\"terms\" className=\"text-[11px] text-[#38383a]\">\r\n              By checking this box, you agree with AI Interview{\" \"}\r\n              <span className=\"text-primary cursor-pointer font-medium\">\r\n                Terms of use\r\n              </span>\r\n              .\r\n            </label>\r\n          </div>\r\n          <div className=\"flex justify-center\">\r\n            <Button\r\n              disabled={!isChecked}\r\n              variant=\"default\"\r\n              size=\"lg\"\r\n              className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n              onClick={() => onNext && onNext()}\r\n            >\r\n            Proceed\r\n              <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n            </Button>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewInstructions;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAHA;;;;;AAeA,MAAM,sBAAsB;IAC1B;IACA;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,qBAAqB;IACzB;IACA;IACA;IACA;CACD;AAED,MAAM,wBAA8D,CAAC,EACnE,gBAAgB,UAAU,EAC1B,WAAW,iBAAiB,EAC5B,YAAY;IAAC;IAAW;CAAU,EAClC,eAAe,mBAAmB,EAClC,uBAAuB,kBAAkB,EACzC,cAAc,kBAAkB,EAChC,MAAM,EACP;IACC,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3C,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAE,WAAU;8BAA6B;;;;;;8BAG1C,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;;wCAAgB;wCAAO;wCAAc;;;;;;;8CAClD,8OAAC;oCAAE,WAAU;;wCAAe;wCAEM;wCAAS;;;;;;;;;;;;;sCAI7C,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;8CACX,UAAU,GAAG,CAAC,CAAC,UAAU,sBACxB,8OAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;8CACX,aAAa,GAAG,CAAC,CAAC,aAAa,sBAC9B,8OAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;8CACX,qBAAqB,GAAG,CAAC,CAAC,MAAM,sBAC/B,8OAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,8OAAC;;8CACC,8OAAC;oCAAE,WAAU;8CAA6B;;;;;;8CAC1C,8OAAC;oCAAG,WAAU;8CACX,YAAY,GAAG,CAAC,CAAC,YAAY,sBAC5B,8OAAC;sDAAgB;2CAAR;;;;;;;;;;;;;;;;sCAKf,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCACC,MAAK;oCACL,IAAG;oCACH,SAAS;oCACT,UAAU,CAAC,IAAM,aAAa,EAAE,MAAM,CAAC,OAAO;oCAC9C,WAAU;;;;;;8CAEZ,8OAAC;oCAAM,SAAQ;oCAAQ,WAAU;;wCAA6B;wCACV;sDAClD,8OAAC;4CAAK,WAAU;sDAA0C;;;;;;wCAEnD;;;;;;;;;;;;;sCAIX,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;gCACL,UAAU,CAAC;gCACX,SAAQ;gCACR,MAAK;gCACL,WAAU;gCACV,SAAS,IAAM,UAAU;;oCAC1B;kDAEC,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpC;uCAEe", "debugId": null}}, {"offset": {"line": 378, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/JobInfoCard.tsx"], "sourcesContent": ["import { MapPin, BriefcaseBusiness } from \"lucide-react\";\r\n\r\nconst JobInfoCard = () => {\r\n  return (\r\n    <div className=\"bg-white p-4 rounded-2xl shadow-sm mb-6 max-w-xl\">\r\n      <div className=\"flex justify-between items-start\">\r\n        <div>\r\n          <h2 className=\"text-xl font-semibold mb-3\">\r\n            UX/UI Designer for Ai-Interview Web App\r\n          </h2>\r\n          <div className=\"flex gap-2 leading-relaxed mb-3 flex-wrap\">\r\n            <p className=\"text-sm text-gray-600 font-medium\">\r\n              $500 - $1000 <span className=\"font-extrabold px-1\">·</span>\r\n            </p>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <MapPin className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">New York</p>\r\n            </div>\r\n            <div className=\"flex gap-1 items-center\">\r\n              <BriefcaseBusiness className=\"w-4 h-5\" />\r\n              <p className=\"text-sm text-gray-600 font-medium\">\r\n                Onsite / Remote\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <p className=\"text-sm text-gray-500 mt-1\">\r\n            We&apos;re building an AI-powered interview tool. We expect you to\r\n            help users prepare by giving human interview experience generation.\r\n          </p>\r\n        </div>\r\n        <span className=\"text-xs bg-[#CCFFB1] text-green-700 px-3 py-1 rounded-full font-medium\">\r\n          Active\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default JobInfoCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;;;AAEA,MAAM,cAAc;IAClB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;;sCACC,8OAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAG3C,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;;wCAAoC;sDAClC,8OAAC;4CAAK,WAAU;sDAAsB;;;;;;;;;;;;8CAErD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;sDAClB,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;8CAEnD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,gOAAA,CAAA,oBAAiB;4CAAC,WAAU;;;;;;sDAC7B,8OAAC;4CAAE,WAAU;sDAAoC;;;;;;;;;;;;;;;;;;sCAKrD,8OAAC;4BAAE,WAAU;sCAA6B;;;;;;;;;;;;8BAK5C,8OAAC;oBAAK,WAAU;8BAAyE;;;;;;;;;;;;;;;;;AAMjG;uCAEe", "debugId": null}}, {"offset": {"line": 518, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/lib/interview-api.ts"], "sourcesContent": ["export interface ConversationMessage {\r\n  role: 'candidate' | 'interviewer';\r\n  content: string;\r\n}\r\n\r\nexport interface InterviewRequest {\r\n  position: string;\r\n  name: string;\r\n  experience: number;\r\n  history: ConversationMessage[];\r\n}\r\n\r\nexport interface ScoreCard {\r\n  technicalSkills: number;\r\n  problemSolving: number;\r\n  communication: number;\r\n  experience: number;\r\n  overall: number;\r\n}\r\n\r\nexport interface Summary {\r\n  ScoreCard: ScoreCard;\r\n  recommendation: string;\r\n  reason: string;\r\n}\r\n\r\nexport interface InterviewResponse {\r\n  nextQuestion: string;\r\n  currentQuestionScore: number;\r\n  isInterviewCompleted: boolean;\r\n  Summary?: Summary;\r\n}\r\n\r\nclass InterviewApiService {\r\n  private baseUrl: string;\r\n\r\n  constructor() {\r\n    this.baseUrl = 'https://interview-server-delta.vercel.app';\r\n  }\r\n\r\n  async sendInterviewRequest(request: InterviewRequest): Promise<InterviewResponse> {\r\n    try {\r\n      const response = await fetch(`${this.baseUrl}/interview`, {\r\n        method: 'POST',\r\n        headers: {\r\n          'Content-Type': 'application/json',\r\n        },\r\n        body: JSON.stringify(request),\r\n      });\r\n\r\n      if (!response.ok) {\r\n        const errorText = await response.text();\r\n        throw new Error(`Interview API Error: ${response.status} ${response.statusText} - ${errorText}`);\r\n      }\r\n\r\n      const data: InterviewResponse = await response.json();\r\n      return data;\r\n    } catch (error) {\r\n      console.error('Failed to send interview request:', error);\r\n      throw error;\r\n    }\r\n  }\r\n\r\n  async startInterview(position: string, name: string, experience: number): Promise<InterviewResponse> {\r\n    const request: InterviewRequest = {\r\n      position,\r\n      name,\r\n      experience,\r\n      history: []\r\n    };\r\n\r\n    const response = await this.sendInterviewRequest(request);\r\n    return response;\r\n  }\r\n\r\n  async continueInterview(\r\n    position: string,\r\n    name: string,\r\n    experience: number,\r\n    history: ConversationMessage[]\r\n  ): Promise<InterviewResponse> {\r\n    const request: InterviewRequest = {\r\n      position,\r\n      name,\r\n      experience,\r\n      history\r\n    };\r\n\r\n    const response = await this.sendInterviewRequest(request);\r\n    return response;\r\n  }\r\n}\r\n\r\n// Export singleton instance\r\nexport const interviewApi = new InterviewApiService();\r\n"], "names": [], "mappings": ";;;AAiCA,MAAM;IACI,QAAgB;IAExB,aAAc;QACZ,IAAI,CAAC,OAAO,GAAG;IACjB;IAEA,MAAM,qBAAqB,OAAyB,EAA8B;QAChF,IAAI;YACF,MAAM,WAAW,MAAM,MAAM,GAAG,IAAI,CAAC,OAAO,CAAC,UAAU,CAAC,EAAE;gBACxD,QAAQ;gBACR,SAAS;oBACP,gBAAgB;gBAClB;gBACA,MAAM,KAAK,SAAS,CAAC;YACvB;YAEA,IAAI,CAAC,SAAS,EAAE,EAAE;gBAChB,MAAM,YAAY,MAAM,SAAS,IAAI;gBACrC,MAAM,IAAI,MAAM,CAAC,qBAAqB,EAAE,SAAS,MAAM,CAAC,CAAC,EAAE,SAAS,UAAU,CAAC,GAAG,EAAE,WAAW;YACjG;YAEA,MAAM,OAA0B,MAAM,SAAS,IAAI;YACnD,OAAO;QACT,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,qCAAqC;YACnD,MAAM;QACR;IACF;IAEA,MAAM,eAAe,QAAgB,EAAE,IAAY,EAAE,UAAkB,EAA8B;QACnG,MAAM,UAA4B;YAChC;YACA;YACA;YACA,SAAS,EAAE;QACb;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,oBAAoB,CAAC;QACjD,OAAO;IACT;IAEA,MAAM,kBACJ,QAAgB,EAChB,IAAY,EACZ,UAAkB,EAClB,OAA8B,EACF;QAC5B,MAAM,UAA4B;YAChC;YACA;YACA;YACA;QACF;QAEA,MAAM,WAAW,MAAM,IAAI,CAAC,oBAAoB,CAAC;QACjD,OAAO;IACT;AACF;AAGO,MAAM,eAAe,IAAI", "debugId": null}}, {"offset": {"line": 574, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/context/InterviewContext.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { createContext, useContext, useState, useCallback, ReactNode } from \"react\";\r\nimport { interviewApi, ConversationMessage, InterviewResponse, Summary } from \"@/lib/interview-api\";\r\n\r\ninterface InterviewContextType {\r\n  // Interview state\r\n  currentQuestion: string;\r\n  setCurrentQuestion: (question: string) => void;\r\n  isInterviewStarted: boolean;\r\n  setIsInterviewStarted: (started: boolean) => void;\r\n  isLoading: boolean;\r\n  setIsLoading: (loading: boolean) => void;\r\n\r\n  // Interview data\r\n  candidateName: string;\r\n  setCandidateName: (name: string) => void;\r\n  jobTitle: string;\r\n  setJobTitle: (title: string) => void;\r\n  experience: number;\r\n  setExperience: (exp: number) => void;\r\n\r\n  // Interview response data\r\n  currentQuestionScore: number;\r\n  totalScore: number;\r\n  questionCount: number;\r\n  questionScores: number[]; // Array to track individual question scores\r\n  isInterviewCompleted: boolean;\r\n  interviewSummary: Summary | null;\r\n\r\n  // Conversation history\r\n  conversationHistory: ConversationMessage[];\r\n  addToHistory: (message: ConversationMessage) => void;\r\n\r\n  // API methods\r\n  startInterview: () => Promise<void>;\r\n  submitAnswer: (answer: string) => Promise<void>;\r\n\r\n  // Error handling\r\n  error: string | null;\r\n  setError: (error: string | null) => void;\r\n}\r\n\r\nconst InterviewContext = createContext<InterviewContextType | undefined>(undefined);\r\n\r\ninterface InterviewProviderProps {\r\n  children: ReactNode;\r\n}\r\n\r\nexport const InterviewProvider: React.FC<InterviewProviderProps> = ({ children }) => {\r\n  // Interview state\r\n  const [currentQuestion, setCurrentQuestion] = useState<string>(\"\");\r\n  const [isInterviewStarted, setIsInterviewStarted] = useState<boolean>(false);\r\n  const [isLoading, setIsLoading] = useState<boolean>(false);\r\n\r\n  // Interview data\r\n  const [candidateName, setCandidateName] = useState<string>(\"Jonathan\");\r\n  const [jobTitle, setJobTitle] = useState<string>(\"Insurance Agent\");\r\n  const [experience, setExperience] = useState<number>(3);\r\n\r\n  // Interview response data\r\n  const [currentQuestionScore, setCurrentQuestionScore] = useState<number>(0);\r\n  const [totalScore, setTotalScore] = useState<number>(0);\r\n  const [questionCount, setQuestionCount] = useState<number>(0);\r\n  const [questionScores, setQuestionScores] = useState<number[]>([]);\r\n  const [isInterviewCompleted, setIsInterviewCompleted] = useState<boolean>(false);\r\n  const [interviewSummary, setInterviewSummary] = useState<Summary | null>(null);\r\n\r\n  // Conversation history\r\n  const [conversationHistory, setConversationHistory] = useState<ConversationMessage[]>([]);\r\n\r\n  // Error handling\r\n  const [error, setError] = useState<string | null>(null);\r\n\r\n  // Helper function to add messages to conversation history\r\n  const addToHistory = useCallback((message: ConversationMessage) => {\r\n    setConversationHistory(prev => [...prev, message]);\r\n  }, []);\r\n\r\n  // Start the interview by getting the first question\r\n  const startInterview = useCallback(async () => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      const response = await interviewApi.startInterview(jobTitle, candidateName, experience);\r\n      setCurrentQuestion(response.nextQuestion);\r\n      setCurrentQuestionScore(response.currentQuestionScore);\r\n      setIsInterviewCompleted(response.isInterviewCompleted);\r\n\r\n      if (response.Summary) {\r\n        setInterviewSummary(response.Summary);\r\n      }\r\n\r\n      // Add interviewer's first question to history\r\n      addToHistory({\r\n        role: 'interviewer',\r\n        content: response.nextQuestion\r\n      });\r\n\r\n      setIsInterviewStarted(true);\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : \"Failed to start interview\";\r\n      setError(errorMessage);\r\n      console.error(\"Failed to start interview:\", err);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [jobTitle, candidateName, experience, addToHistory]);\r\n\r\n  // Submit candidate's answer and get next question\r\n  const submitAnswer = useCallback(async (answer: string) => {\r\n    setIsLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      // Add candidate's answer to history\r\n      const candidateMessage: ConversationMessage = {\r\n        role: 'candidate',\r\n        content: answer\r\n      };\r\n\r\n      const updatedHistory = [...conversationHistory, candidateMessage];\r\n      addToHistory(candidateMessage);\r\n\r\n      // Get next question from API\r\n      const response = await interviewApi.continueInterview(\r\n        jobTitle,\r\n        candidateName,\r\n        experience,\r\n        updatedHistory\r\n      );\r\n\r\n      setCurrentQuestion(response.nextQuestion);\r\n      setCurrentQuestionScore(response.currentQuestionScore);\r\n      setIsInterviewCompleted(response.isInterviewCompleted);\r\n\r\n      // Add current question score to total score and track individual scores\r\n      if (response.currentQuestionScore > 0) {\r\n        setTotalScore(prev => prev + response.currentQuestionScore);\r\n        setQuestionCount(prev => prev + 1);\r\n        setQuestionScores(prev => [...prev, response.currentQuestionScore]);\r\n      }\r\n\r\n      if (response.Summary) {\r\n        setInterviewSummary(response.Summary);\r\n      }\r\n\r\n      // Add interviewer's next question to history (only if not completed)\r\n      if (!response.isInterviewCompleted && response.nextQuestion) {\r\n        addToHistory({\r\n          role: 'interviewer',\r\n          content: response.nextQuestion\r\n        });\r\n      }\r\n\r\n    } catch (err) {\r\n      const errorMessage = err instanceof Error ? err.message : \"Failed to submit answer\";\r\n      setError(errorMessage);\r\n      console.error(\"Failed to submit answer:\", err);\r\n    } finally {\r\n      setIsLoading(false);\r\n    }\r\n  }, [jobTitle, candidateName, experience, conversationHistory, addToHistory]);\r\n\r\n  const value: InterviewContextType = {\r\n    // Interview state\r\n    currentQuestion,\r\n    setCurrentQuestion,\r\n    isInterviewStarted,\r\n    setIsInterviewStarted,\r\n    isLoading,\r\n    setIsLoading,\r\n\r\n    // Interview data\r\n    candidateName,\r\n    setCandidateName,\r\n    jobTitle,\r\n    setJobTitle,\r\n    experience,\r\n    setExperience,\r\n\r\n    // Interview response data\r\n    currentQuestionScore,\r\n    totalScore,\r\n    questionCount,\r\n    questionScores,\r\n    isInterviewCompleted,\r\n    interviewSummary,\r\n\r\n    // Conversation history\r\n    conversationHistory,\r\n    addToHistory,\r\n\r\n    // API methods\r\n    startInterview,\r\n    submitAnswer,\r\n\r\n    // Error handling\r\n    error,\r\n    setError,\r\n  };\r\n\r\n  return (\r\n    <InterviewContext.Provider value={value}>\r\n      {children}\r\n    </InterviewContext.Provider>\r\n  );\r\n};\r\n\r\nexport const useInterview = (): InterviewContextType => {\r\n  const context = useContext(InterviewContext);\r\n  if (context === undefined) {\r\n    throw new Error('useInterview must be used within an InterviewProvider');\r\n  }\r\n  return context;\r\n};\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAFA;;;;AA0CA,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAAoC;AAMlE,MAAM,oBAAsD,CAAC,EAAE,QAAQ,EAAE;IAC9E,kBAAkB;IAClB,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC/D,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IACtE,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAEpD,iBAAiB;IACjB,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACjD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAErD,0BAA0B;IAC1B,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzE,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACjE,MAAM,CAAC,sBAAsB,wBAAwB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAC1E,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAkB;IAEzE,uBAAuB;IACvB,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAyB,EAAE;IAExF,iBAAiB;IACjB,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,0DAA0D;IAC1D,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,CAAC;QAChC,uBAAuB,CAAA,OAAQ;mBAAI;gBAAM;aAAQ;IACnD,GAAG,EAAE;IAEL,oDAAoD;IACpD,MAAM,iBAAiB,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE;QACjC,aAAa;QACb,SAAS;QAET,IAAI;YACF,MAAM,WAAW,MAAM,uHAAA,CAAA,eAAY,CAAC,cAAc,CAAC,UAAU,eAAe;YAC5E,mBAAmB,SAAS,YAAY;YACxC,wBAAwB,SAAS,oBAAoB;YACrD,wBAAwB,SAAS,oBAAoB;YAErD,IAAI,SAAS,OAAO,EAAE;gBACpB,oBAAoB,SAAS,OAAO;YACtC;YAEA,8CAA8C;YAC9C,aAAa;gBACX,MAAM;gBACN,SAAS,SAAS,YAAY;YAChC;YAEA,sBAAsB;QACxB,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,QAAQ,KAAK,CAAC,8BAA8B;QAC9C,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAU;QAAe;QAAY;KAAa;IAEtD,kDAAkD;IAClD,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,cAAW,AAAD,EAAE,OAAO;QACtC,aAAa;QACb,SAAS;QAET,IAAI;YACF,oCAAoC;YACpC,MAAM,mBAAwC;gBAC5C,MAAM;gBACN,SAAS;YACX;YAEA,MAAM,iBAAiB;mBAAI;gBAAqB;aAAiB;YACjE,aAAa;YAEb,6BAA6B;YAC7B,MAAM,WAAW,MAAM,uHAAA,CAAA,eAAY,CAAC,iBAAiB,CACnD,UACA,eACA,YACA;YAGF,mBAAmB,SAAS,YAAY;YACxC,wBAAwB,SAAS,oBAAoB;YACrD,wBAAwB,SAAS,oBAAoB;YAErD,wEAAwE;YACxE,IAAI,SAAS,oBAAoB,GAAG,GAAG;gBACrC,cAAc,CAAA,OAAQ,OAAO,SAAS,oBAAoB;gBAC1D,iBAAiB,CAAA,OAAQ,OAAO;gBAChC,kBAAkB,CAAA,OAAQ;2BAAI;wBAAM,SAAS,oBAAoB;qBAAC;YACpE;YAEA,IAAI,SAAS,OAAO,EAAE;gBACpB,oBAAoB,SAAS,OAAO;YACtC;YAEA,qEAAqE;YACrE,IAAI,CAAC,SAAS,oBAAoB,IAAI,SAAS,YAAY,EAAE;gBAC3D,aAAa;oBACX,MAAM;oBACN,SAAS,SAAS,YAAY;gBAChC;YACF;QAEF,EAAE,OAAO,KAAK;YACZ,MAAM,eAAe,eAAe,QAAQ,IAAI,OAAO,GAAG;YAC1D,SAAS;YACT,QAAQ,KAAK,CAAC,4BAA4B;QAC5C,SAAU;YACR,aAAa;QACf;IACF,GAAG;QAAC;QAAU;QAAe;QAAY;QAAqB;KAAa;IAE3E,MAAM,QAA8B;QAClC,kBAAkB;QAClB;QACA;QACA;QACA;QACA;QACA;QAEA,iBAAiB;QACjB;QACA;QACA;QACA;QACA;QACA;QAEA,0BAA0B;QAC1B;QACA;QACA;QACA;QACA;QACA;QAEA,uBAAuB;QACvB;QACA;QAEA,cAAc;QACd;QACA;QAEA,iBAAiB;QACjB;QACA;IACF;IAEA,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;kBAC/B;;;;;;AAGP;AAEO,MAAM,eAAe;IAC1B,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 751, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/QuestionsList.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useInterview } from \"@/context/InterviewContext\";\r\n\r\ntype QuestionsListProps = {\r\n  className?: string;\r\n};\r\n\r\nconst QuestionsList = ({ className }: QuestionsListProps) => {\r\n  const { conversationHistory, isInterviewStarted } = useInterview();\r\n\r\n  // Filter to show only interviewer questions\r\n  const interviewerQuestions = conversationHistory.filter(msg => msg.role === 'interviewer');\r\n\r\n  return (\r\n    <div\r\n      className={`rounded-2xl bg-white p-4 w-full shadow-sm overflow-y-auto scrollbar-hidden ${\r\n        className || \"\"\r\n      }`}\r\n    >\r\n      <h3 className=\"font-semibold text-lg mb-6\">Video Transcript</h3>\r\n\r\n      {!isInterviewStarted ? (\r\n        <p className=\"text-gray-500 text-center py-8\">Interview not started yet</p>\r\n      ) : interviewerQuestions.length === 0 ? (\r\n        <p className=\"text-gray-500 text-center py-8\">Loading questions...</p>\r\n      ) : (\r\n        <ul className=\"space-y-4\">\r\n          {interviewerQuestions.map((question, i) => (\r\n            <li\r\n              key={i}\r\n              className=\"relative flex items-start space-x-3 p-3 bg-gray-50 rounded-lg\"\r\n            >\r\n              <div className=\"rounded-full w-8 h-8 flex items-center justify-center text-sm font-medium bg-[#6938EF] text-white flex-shrink-0\">\r\n                {i + 1}\r\n              </div>\r\n              <div className=\"flex-1 min-w-0\">\r\n                <p className=\"text-sm text-gray-800 leading-relaxed\">\r\n                  {question.content}\r\n                </p>\r\n              </div>\r\n            </li>\r\n          ))}\r\n        </ul>\r\n      )}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default QuestionsList;\r\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAOA,MAAM,gBAAgB,CAAC,EAAE,SAAS,EAAsB;IACtD,MAAM,EAAE,mBAAmB,EAAE,kBAAkB,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAE/D,4CAA4C;IAC5C,MAAM,uBAAuB,oBAAoB,MAAM,CAAC,CAAA,MAAO,IAAI,IAAI,KAAK;IAE5E,qBACE,8OAAC;QACC,WAAW,CAAC,2EAA2E,EACrF,aAAa,IACb;;0BAEF,8OAAC;gBAAG,WAAU;0BAA6B;;;;;;YAE1C,CAAC,mCACA,8OAAC;gBAAE,WAAU;0BAAiC;;;;;uBAC5C,qBAAqB,MAAM,KAAK,kBAClC,8OAAC;gBAAE,WAAU;0BAAiC;;;;;qCAE9C,8OAAC;gBAAG,WAAU;0BACX,qBAAqB,GAAG,CAAC,CAAC,UAAU,kBACnC,8OAAC;wBAEC,WAAU;;0CAEV,8OAAC;gCAAI,WAAU;0CACZ,IAAI;;;;;;0CAEP,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CACV,SAAS,OAAO;;;;;;;;;;;;uBARhB;;;;;;;;;;;;;;;;AAiBnB;uCAEe", "debugId": null}}, {"offset": {"line": 841, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/InterviewLayout.tsx"], "sourcesContent": ["import { ReactNode } from \"react\";\r\n\r\nconst InterviewLayout = ({ children }: { children: ReactNode }) => {\r\n  return (\r\n    <div className=\"border rounded-lg p-6 min-h-[600px] mb-4 flex-1\">\r\n      {children}\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewLayout;\r\n"], "names": [], "mappings": ";;;;;AAEA,MAAM,kBAAkB,CAAC,EAAE,QAAQ,EAA2B;IAC5D,qBACE,8OAAC;QAAI,WAAU;kBACZ;;;;;;AAGP;uCAEe", "debugId": null}}, {"offset": {"line": 863, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/InterviewWithDID.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport { ArrowRight, CheckCircle } from \"lucide-react\";\r\nimport JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport { useInterview } from \"@/context/InterviewContext\";\r\n\r\ntype InterviewWithDIDProps = {\r\n  onNext?: () => void;\r\n};\r\n\r\nconst InterviewWithDID: React.FC<InterviewWithDIDProps> = ({\r\n  onNext,\r\n}) => {\r\n  const {\r\n    currentQuestion,\r\n    isInterviewStarted,\r\n    isLoading,\r\n    startInterview: startInterviewAPI,\r\n    submitAnswer,\r\n    error,\r\n    isInterviewCompleted,\r\n    interviewSummary,\r\n  } = useInterview();\r\n\r\n  const [showSubmitButton, setShowSubmitButton] = useState<boolean>(false);\r\n  const [candidateAnswer, setCandidateAnswer] = useState<string>(\"\");\r\n\r\n  const startInterview = async () => {\r\n    try {\r\n      await startInterviewAPI();\r\n      setShowSubmitButton(true);\r\n    } catch (err) {\r\n      console.error(\"Failed to start interview:\", err);\r\n    }\r\n  };\r\n\r\n\r\n\r\n\r\n  const handleSubmitAnswer = async () => {\r\n    if (!candidateAnswer.trim()) {\r\n      alert(\"Please provide an answer before continuing.\");\r\n      return;\r\n    }\r\n\r\n    try {\r\n      setShowSubmitButton(false);\r\n      await submitAnswer(candidateAnswer);\r\n      setCandidateAnswer(\"\");\r\n      setShowSubmitButton(true);\r\n    } catch (err) {\r\n      console.error(\"Failed to submit answer:\", err);\r\n      setShowSubmitButton(true);\r\n    }\r\n  };\r\n\r\n  if (!isInterviewStarted) {\r\n    return (\r\n      <div className=\"h-screen\">\r\n        <JobInfoCard />\r\n        <InterviewLayout>\r\n          <div className=\"flex flex-col md:flex-row gap-10 justify-center items-center md:items-start\">\r\n            <QuestionsList className=\"h-[550px]\" />\r\n            {/* <CandidateWithAgent\r\n              className=\"w-[300px] h-[300px]\"\r\n              // useAgent={true}\r\n              candidateName={candidateName}\r\n              jobTitle={jobTitle}\r\n            /> */}\r\n          </div>\r\n\r\n          <div className=\"flex justify-center mt-10 gap-4\">\r\n            <Button\r\n              variant=\"default\"\r\n              className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n              onClick={startInterview}\r\n            >\r\n              Start Interview\r\n              <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n            </Button>\r\n          </div>\r\n          \r\n          <div className=\"flex justify-center mt-5 text-2xl font-semibold text-primary\">\r\n            Ready to begin\r\n          </div>\r\n        </InterviewLayout>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  if (isInterviewCompleted) {\r\n    return (\r\n      <div className=\"h-screen\">\r\n        <JobInfoCard />\r\n        <InterviewLayout>\r\n          <div className=\"flex flex-col items-center justify-center h-full\">\r\n            <div className=\"text-center mb-8\">\r\n              <CheckCircle className=\"w-16 h-16 text-green-500 mx-auto mb-4\" />\r\n              <h2 className=\"text-2xl font-bold text-gray-800 mb-2\">\r\n                Interview Completed!\r\n              </h2>\r\n              <p className=\"text-gray-600 mb-4\">\r\n                Thank you for completing the interview. Your responses have been recorded.\r\n              </p>\r\n\r\n              {interviewSummary && (\r\n                <div className=\"bg-white rounded-lg shadow-lg p-6 max-w-md mx-auto mb-6\">\r\n                  <h3 className=\"text-lg font-semibold mb-4\">Detailed Interview Summary</h3>\r\n                  <div className=\"space-y-2 text-sm\">\r\n                    <div className=\"flex justify-between\">\r\n                      <span>Technical Skills:</span>\r\n                      <span className=\"font-medium\">{interviewSummary.ScoreCard.technicalSkills}/20</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between\">\r\n                      <span>Problem Solving:</span>\r\n                      <span className=\"font-medium\">{interviewSummary.ScoreCard.problemSolving}/20</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between\">\r\n                      <span>Communication:</span>\r\n                      <span className=\"font-medium\">{interviewSummary.ScoreCard.communication}/20</span>\r\n                    </div>\r\n                    <div className=\"flex justify-between\">\r\n                      <span>Experience:</span>\r\n                      <span className=\"font-medium\">{interviewSummary.ScoreCard.experience}/20</span>\r\n                    </div>\r\n                    <hr className=\"my-2\" />\r\n                    <div className=\"flex justify-between font-semibold text-base\">\r\n                      <span>Total Score:</span>\r\n                      <span>{interviewSummary.ScoreCard.overall}/100</span>\r\n                    </div>\r\n                    <div className=\"mt-4\">\r\n                      <div className=\"text-center\">\r\n                        <span className={`px-3 py-1 rounded-full text-sm font-medium ${\r\n                          interviewSummary.recommendation === 'HIRE' ? 'bg-green-100 text-green-800' :\r\n                          interviewSummary.recommendation === 'REJECT' ? 'bg-red-100 text-red-800' :\r\n                          'bg-yellow-100 text-yellow-800'\r\n                        }`}>\r\n                          {interviewSummary.recommendation}\r\n                        </span>\r\n                      </div>\r\n                      <p className=\"text-xs text-gray-600 mt-2\">{interviewSummary.reason}</p>\r\n                    </div>\r\n                  </div>\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            <Button\r\n              variant=\"default\"\r\n              className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n              onClick={() => onNext?.()}\r\n            >\r\n              View Results\r\n              <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n            </Button>\r\n          </div>\r\n        </InterviewLayout>\r\n      </div>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <JobInfoCard />\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-10 justify-center items-center lg:items-start\">\r\n\r\n          <div className=\"flex-1 max-w-2xl\">\r\n            {/* Current Question Display */}\r\n            <div className=\"bg-white rounded-lg shadow-lg p-6 mb-6\">\r\n              <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Question:</h3>\r\n              <p className=\"text-gray-700 text-lg leading-relaxed\">\r\n                {currentQuestion || \"Loading question...\"}\r\n              </p>\r\n              {error && (\r\n                <div className=\"mt-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded\">\r\n                  Error: {error}\r\n                </div>\r\n              )}\r\n            </div>\r\n\r\n            {/* Answer Input */}\r\n            {showSubmitButton && (\r\n              <div className=\"bg-white rounded-lg shadow-lg p-6\">\r\n                <h3 className=\"text-lg font-semibold text-gray-800 mb-4\">Your Answer:</h3>\r\n                <textarea\r\n                  value={candidateAnswer}\r\n                  onChange={(e) => setCandidateAnswer(e.target.value)}\r\n                  placeholder=\"Type your answer here...\"\r\n                  className=\"w-full p-3 border border-gray-300 rounded-lg resize-none focus:ring-2 focus:ring-blue-500 focus:border-transparent\"\r\n                  rows={4}\r\n                />\r\n              </div>\r\n            )}\r\n          </div>\r\n           <QuestionsList className=\"h-[400px] lg:w-80\" />\r\n\r\n\r\n          {/* <CandidateWithAgent\r\n            className=\"w-[300px] h-[300px]\"\r\n            candidateName={candidateName}\r\n            jobTitle={jobTitle}\r\n            useAgent={false}\r\n          /> */}\r\n        </div>\r\n\r\n        <div className=\"flex justify-center mt-10 gap-4\">\r\n          {showSubmitButton && !isLoading ? (\r\n            <Button\r\n              variant=\"default\"\r\n              className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n              onClick={handleSubmitAnswer}\r\n            >\r\n              {isInterviewCompleted ? \"Finish Interview\" : \"Submit Answer\"}\r\n              <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n            </Button>\r\n          ) : (\r\n            <div className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center justify-center gap-2 bg-gray-200 text-gray-500\">\r\n              {isLoading ? \"Loading question...\" : \"Listen to the question\"}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n\r\n      </InterviewLayout>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewWithDID;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAPA;;;;;;;;;AAaA,MAAM,mBAAoD,CAAC,EACzD,MAAM,EACP;IACC,MAAM,EACJ,eAAe,EACf,kBAAkB,EAClB,SAAS,EACT,gBAAgB,iBAAiB,EACjC,YAAY,EACZ,KAAK,EACL,oBAAoB,EACpB,gBAAgB,EACjB,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAEf,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAW;IAClE,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IAE/D,MAAM,iBAAiB;QACrB,IAAI;YACF,MAAM;YACN,oBAAoB;QACtB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,8BAA8B;QAC9C;IACF;IAKA,MAAM,qBAAqB;QACzB,IAAI,CAAC,gBAAgB,IAAI,IAAI;YAC3B,MAAM;YACN;QACF;QAEA,IAAI;YACF,oBAAoB;YACpB,MAAM,aAAa;YACnB,mBAAmB;YACnB,oBAAoB;QACtB,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,4BAA4B;YAC1C,oBAAoB;QACtB;IACF;IAEA,IAAI,CAAC,oBAAoB;QACvB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0HAAA,CAAA,UAAW;;;;;8BACZ,8OAAC,8HAAA,CAAA,UAAe;;sCACd,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4HAAA,CAAA,UAAa;gCAAC,WAAU;;;;;;;;;;;sCAS3B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS;;oCACV;kDAEC,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;sCAI1B,8OAAC;4BAAI,WAAU;sCAA+D;;;;;;;;;;;;;;;;;;IAMtF;IAEA,IAAI,sBAAsB;QACxB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC,0HAAA,CAAA,UAAW;;;;;8BACZ,8OAAC,8HAAA,CAAA,UAAe;8BACd,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,2NAAA,CAAA,cAAW;wCAAC,WAAU;;;;;;kDACvB,8OAAC;wCAAG,WAAU;kDAAwC;;;;;;kDAGtD,8OAAC;wCAAE,WAAU;kDAAqB;;;;;;oCAIjC,kCACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA6B;;;;;;0DAC3C,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;;oEAAe,iBAAiB,SAAS,CAAC,eAAe;oEAAC;;;;;;;;;;;;;kEAE5E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;;oEAAe,iBAAiB,SAAS,CAAC,cAAc;oEAAC;;;;;;;;;;;;;kEAE3E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;;oEAAe,iBAAiB,SAAS,CAAC,aAAa;oEAAC;;;;;;;;;;;;;kEAE1E,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;gEAAK,WAAU;;oEAAe,iBAAiB,SAAS,CAAC,UAAU;oEAAC;;;;;;;;;;;;;kEAEvE,8OAAC;wDAAG,WAAU;;;;;;kEACd,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;0EAAK;;;;;;0EACN,8OAAC;;oEAAM,iBAAiB,SAAS,CAAC,OAAO;oEAAC;;;;;;;;;;;;;kEAE5C,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACb,cAAA,8OAAC;oEAAK,WAAW,CAAC,2CAA2C,EAC3D,iBAAiB,cAAc,KAAK,SAAS,gCAC7C,iBAAiB,cAAc,KAAK,WAAW,4BAC/C,iCACA;8EACC,iBAAiB,cAAc;;;;;;;;;;;0EAGpC,8OAAC;gEAAE,WAAU;0EAA8B,iBAAiB,MAAM;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAO5E,8OAAC,2HAAA,CAAA,SAAM;gCACL,SAAQ;gCACR,WAAU;gCACV,SAAS,IAAM;;oCAChB;kDAEC,8OAAC,kNAAA,CAAA,aAAU;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IAMlC;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0HAAA,CAAA,UAAW;;;;;0BACZ,8OAAC,8HAAA,CAAA,UAAe;;kCACd,8OAAC;wBAAI,WAAU;;0CAEb,8OAAC;gCAAI,WAAU;;kDAEb,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDAAE,WAAU;0DACV,mBAAmB;;;;;;4CAErB,uBACC,8OAAC;gDAAI,WAAU;;oDAAiE;oDACtE;;;;;;;;;;;;;oCAMb,kCACC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAG,WAAU;0DAA2C;;;;;;0DACzD,8OAAC;gDACC,OAAO;gDACP,UAAU,CAAC,IAAM,mBAAmB,EAAE,MAAM,CAAC,KAAK;gDAClD,aAAY;gDACZ,WAAU;gDACV,MAAM;;;;;;;;;;;;;;;;;;0CAKb,8OAAC,4HAAA,CAAA,UAAa;gCAAC,WAAU;;;;;;;;;;;;kCAW5B,8OAAC;wBAAI,WAAU;kCACZ,oBAAoB,CAAC,0BACpB,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS;;gCAER,uBAAuB,qBAAqB;8CAC7C,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;iDAGxB,8OAAC;4BAAI,WAAU;sCACZ,YAAY,wBAAwB;;;;;;;;;;;;;;;;;;;;;;;AASnD;uCAEe", "debugId": null}}, {"offset": {"line": 1416, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/VideoTranscript.tsx"], "sourcesContent": ["\"use client\";\r\nimport { useInterview } from \"@/context/InterviewContext\";\r\n\r\nconst VideoTranscript = () => {\r\n  const { conversationHistory, currentQuestionScore, totalScore } = useInterview();\r\n\r\n  return (\r\n    <div className=\"rounded-2xl bg-white p-4 w-full max-w-[300px] sm:w-[300px] shadow-sm h-[488px] overflow-y-auto scrollbar-hidden\">\r\n      <div className=\"flex justify-between items-center mb-5\">\r\n        <p className=\"text-lg font-semibold text-black\">Interview Transcript</p>\r\n        <div className=\"text-xs text-gray-500\">\r\n          Score: {totalScore}/100\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"space-y-4\">\r\n        {conversationHistory.map((message, index) => (\r\n          <div key={index} className=\"mb-4\">\r\n            {message.role === 'interviewer' ? (\r\n              <div>\r\n                <p className=\"text-sm font-semibold text-blue-600 mb-1\">\r\n                  Question {Math.floor(index / 2) + 1}:\r\n                </p>\r\n                <p className=\"text-sm text-gray-700 leading-relaxed\">\r\n                  {message.content}\r\n                </p>\r\n              </div>\r\n            ) : (\r\n              <div>\r\n                <p className=\"text-sm font-semibold text-green-600 mb-1\">Answer:</p>\r\n                <p className=\"text-sm text-gray-700 leading-relaxed\">\r\n                  {message.content}\r\n                </p>\r\n                {index === conversationHistory.length - 1 && currentQuestionScore > 0 && (\r\n                  <p className=\"text-xs text-blue-500 mt-1\">\r\n                    Score: {currentQuestionScore}/20\r\n                  </p>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n        ))}\r\n\r\n        {conversationHistory.length === 0 && (\r\n          <p className=\"text-sm text-gray-500 italic\">\r\n            Interview transcript will appear here as you progress...\r\n          </p>\r\n        )}\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\nexport default VideoTranscript;\r\n"], "names": [], "mappings": ";;;;AACA;AADA;;;AAGA,MAAM,kBAAkB;IACtB,MAAM,EAAE,mBAAmB,EAAE,oBAAoB,EAAE,UAAU,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,eAAY,AAAD;IAE7E,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAmC;;;;;;kCAChD,8OAAC;wBAAI,WAAU;;4BAAwB;4BAC7B;4BAAW;;;;;;;;;;;;;0BAIvB,8OAAC;gBAAI,WAAU;;oBACZ,oBAAoB,GAAG,CAAC,CAAC,SAAS,sBACjC,8OAAC;4BAAgB,WAAU;sCACxB,QAAQ,IAAI,KAAK,8BAChB,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;;4CAA2C;4CAC5C,KAAK,KAAK,CAAC,QAAQ,KAAK;4CAAE;;;;;;;kDAEtC,8OAAC;wCAAE,WAAU;kDACV,QAAQ,OAAO;;;;;;;;;;;qDAIpB,8OAAC;;kDACC,8OAAC;wCAAE,WAAU;kDAA4C;;;;;;kDACzD,8OAAC;wCAAE,WAAU;kDACV,QAAQ,OAAO;;;;;;oCAEjB,UAAU,oBAAoB,MAAM,GAAG,KAAK,uBAAuB,mBAClE,8OAAC;wCAAE,WAAU;;4CAA6B;4CAChC;4CAAqB;;;;;;;;;;;;;2BAlB7B;;;;;oBA0BX,oBAAoB,MAAM,KAAK,mBAC9B,8OAAC;wBAAE,WAAU;kCAA+B;;;;;;;;;;;;;;;;;;AAOtD;uCACe", "debugId": null}}, {"offset": {"line": 1559, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/FinishInterview.tsx"], "sourcesContent": ["import { ArrowR<PERSON> } from \"lucide-react\";\r\nimport JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport { But<PERSON> } from \"@/components/ui/button\";\r\nimport VideoTranscript from \"@/components/VideoTranscript\";\r\n\r\ntype FinishInterviewProps = {\r\n  onNext?: () => void;\r\n};\r\n\r\nconst FinishInterview = ({ onNext }: FinishInterviewProps) => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <JobInfoCard />\r\n\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\r\n          <QuestionsList />\r\n          {/* <CandidateWithAgent\r\n            className=\" h-[490px]\"\r\n            useAgent={true}\r\n            candidateName=\"Jonathan\"\r\n            jobTitle=\"Insurance Agent\"\r\n            message=\"Thank you for completing the interview. Do you have any final questions?\"\r\n          /> */}\r\n          <VideoTranscript />\r\n        </div>\r\n\r\n        <div className=\"flex justify-center mt-10 gap-4\">\r\n          <Button\r\n            variant=\"default\"\r\n            className=\"py-2 sm:py-6 text-sm sm:text-md rounded-full w-full sm:w-[200px] lg:w-[330px] flex items-center gap-2 group cursor-pointer text-white\"\r\n            onClick={() => onNext && onNext()}\r\n          >\r\n            Finish Interview\r\n            <ArrowRight className=\"w-6 h-6 duration-300 group-hover:translate-x-1\" />\r\n          </Button>\r\n        </div>\r\n      </InterviewLayout>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default FinishInterview;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAMA,MAAM,kBAAkB,CAAC,EAAE,MAAM,EAAwB;IACvD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,0HAAA,CAAA,UAAW;;;;;0BAEZ,8OAAC,8HAAA,CAAA,UAAe;;kCACd,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4HAAA,CAAA,UAAa;;;;;0CAQd,8OAAC,8HAAA,CAAA,UAAe;;;;;;;;;;;kCAGlB,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,2HAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,WAAU;4BACV,SAAS,IAAM,UAAU;;gCAC1B;8CAEC,8OAAC,kNAAA,CAAA,aAAU;oCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAMlC;uCAEe", "debugId": null}}, {"offset": {"line": 1657, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/public/icons/trophy.png.mjs%20%28structured%20image%20object%29"], "sourcesContent": ["import src from \"IMAGE\";\nexport default { src, width: 28, height: 28, blurDataURL: \"data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAgAAAAICAYAAADED76LAAABBUlEQVR42i2JMUvDQBiGv2qpl6slTbSRNLFeqqFncrVJsATUap266F/ooIOLSCY1o11KKDoodWjBRURE0MFdf4H/RLu5Su9KX3jg4X0AS7NoaTEjk2XJCFlufZvjcxefaKBrGXV/S6lusqwdUOwEVHICF9v82yjyBod7ar1Zy3o3UaH7dbvyLbg+K3R3mOQdNJU6BBQ5/UhO3nrk8e+h8S94534X5ROfN6hSTC/a8vHTlX7/8xKOfl/D0XNH75+380esgitAinMLrV3Fd8rI6pxocXKqxcxGVquh+MRAKqRSADV3fpWYSB/G5mB4aQ6Ee25uTbTJhJTNdOmjZ3wKLCNdmpnGMewcPLJUc9zPAAAAAElFTkSuQmCC\", blurWidth: 8, blurHeight: 8 }\n"], "names": [], "mappings": ";;;AAAA;;uCACe;IAAE,KAAA,kHAAA,CAAA,UAAG;IAAE,OAAO;IAAI,QAAQ;IAAI,aAAa;IAAkc,WAAW;IAAG,YAAY;AAAE", "debugId": null}}, {"offset": {"line": 1676, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/InterviewCard.tsx"], "sourcesContent": ["import React from \"react\";\r\nimport Image from \"next/image\";\r\nimport TROPHY from \"@/public/icons/trophy.png\";\r\nconst InterviewCard = () => {\r\n  return (\r\n    <div className=\"flex  justify-between bg-white rounded-2xl shadow-md p-4 w-full max-w-xl mb-5\">\r\n      {/* Left Box: Score Section */}\r\n      <div className=\"flex items-center space-x-4\">\r\n        <div className=\"bg-[#F4F1FE] rounded-xl px-4 py-4 text-center w-30\">\r\n          <div className=\"flex justify-center mb-2\">\r\n            <Image src={TROPHY} alt=\"Trophy\" />\r\n          </div>\r\n          <p className=\"text-xl font-bold text-[#1E1E1E]\">55%</p>\r\n          <p className=\"text-xs text-gray-600 mt-1\">Overall Score</p>\r\n        </div>\r\n\r\n        <div>\r\n          <h3 className=\"font-semibold text-sm sm:text-[6px] md:text-base lg:text-lg text-[#1E1E1E] mb-2\">\r\n            AI Interviewer\r\n          </h3>\r\n          <p className=\"text-sm text-gray-800 font-medium\">UI UX Designer</p>\r\n          <p className=\"text-sm text-gray-800 font-medium\">18th June, 2025</p>\r\n        </div>\r\n      </div>\r\n\r\n      <div className=\"top-0\">\r\n        <span className=\"bg-[#CCFFB1] text-[#1E1E1E] text-xs px-4 py-1 rounded-full\">\r\n          Evaluated\r\n        </span>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default InterviewCard;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;;;;AACA,MAAM,gBAAgB;IACpB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCAAC,KAAK,kRAAA,CAAA,UAAM;oCAAE,KAAI;;;;;;;;;;;0CAE1B,8OAAC;gCAAE,WAAU;0CAAmC;;;;;;0CAChD,8OAAC;gCAAE,WAAU;0CAA6B;;;;;;;;;;;;kCAG5C,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAkF;;;;;;0CAGhG,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;0CACjD,8OAAC;gCAAE,WAAU;0CAAoC;;;;;;;;;;;;;;;;;;0BAIrD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,WAAU;8BAA6D;;;;;;;;;;;;;;;;;AAMrF;uCAEe", "debugId": null}}, {"offset": {"line": 1799, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/ScoreBar.jsx"], "sourcesContent": ["const ScoreBar = ({ label, value, color = \"bg-orange-500\" }) => {\r\n  return (\r\n    <div className=\"mb-2\">\r\n      <div className=\"flex justify-between text-sm mb-1\">\r\n        <span className=\"mb-1\">{label}</span>\r\n        <span>{value}/100</span>\r\n      </div>\r\n      <div className=\"w-full bg-gray-200 rounded-full h-2.5\">\r\n        <div\r\n          className={`h-2.5 rounded-full ${color}`}\r\n          style={{ width: `${value}%` }}\r\n        ></div>\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScoreBar;\r\n"], "names": [], "mappings": ";;;;;AAAA,MAAM,WAAW,CAAC,EAAE,KAAK,EAAE,KAAK,EAAE,QAAQ,eAAe,EAAE;IACzD,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAK,WAAU;kCAAQ;;;;;;kCACxB,8OAAC;;4BAAM;4BAAM;;;;;;;;;;;;;0BAEf,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBACC,WAAW,CAAC,mBAAmB,EAAE,OAAO;oBACxC,OAAO;wBAAE,OAAO,GAAG,MAAM,CAAC,CAAC;oBAAC;;;;;;;;;;;;;;;;;AAKtC;uCAEe", "debugId": null}}, {"offset": {"line": 1866, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/CircularRating.jsx"], "sourcesContent": ["import { CircularProgressbar, buildStyles } from \"react-circular-progressbar\";\r\nimport \"react-circular-progressbar/dist/styles.css\";\r\n\r\nconst CircularRating = ({ label, percent, color, trailColor }) => {\r\n  return (\r\n    <div className=\"flex flex-col items-center space-y-1 mb-2\">\r\n      <p className=\"text-sm font-semibold mb-3\">{label}</p>\r\n      <div className=\"w-32 h-28\">\r\n        <CircularProgressbar\r\n          value={percent}\r\n          text={`${percent}%`}\r\n          strokeWidth={10}\r\n          styles={buildStyles({\r\n            textSize: \"12px\",\r\n            pathColor: color,\r\n            textColor: \"#5a5a5a\",\r\n            trailColor: trailColor,\r\n          })}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default CircularRating;\r\n"], "names": [], "mappings": ";;;;AAAA;;;;AAGA,MAAM,iBAAiB,CAAC,EAAE,KAAK,EAAE,OAAO,EAAE,KAAK,EAAE,UAAU,EAAE;IAC3D,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAE,WAAU;0BAA8B;;;;;;0BAC3C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,wKAAA,CAAA,sBAAmB;oBAClB,OAAO;oBACP,MAAM,GAAG,QAAQ,CAAC,CAAC;oBACnB,aAAa;oBACb,QAAQ,CAAA,GAAA,wKAAA,CAAA,cAAW,AAAD,EAAE;wBAClB,UAAU;wBACV,WAAW;wBACX,WAAW;wBACX,YAAY;oBACd;;;;;;;;;;;;;;;;;AAKV;uCAEe", "debugId": null}}, {"offset": {"line": 1922, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/analysis/ScoreCard.jsx"], "sourcesContent": ["import ScoreBar from \"./ScoreBar\";\r\nimport CircularRating from \"./CircularRating\";\r\n\r\nconst ScoreCard = () => {\r\n  return (\r\n    <div className=\"grid grid-cols-1 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 border p-6 rounded-xl w-full max-w-6xl mx-auto\">\r\n      {/* Resume Score */}\r\n      <div className=\"bg-white rounded-lg p-4 shadow-sm\">\r\n        <div className=\"flex justify-between font-semibold mb-4\">\r\n          <span>Resume Score</span>\r\n          <span>65%</span>\r\n        </div>\r\n        <div className=\"flex flex-col gap-4\">\r\n          <ScoreBar label=\"Company Fit\" value={66} />\r\n          <ScoreBar\r\n            label=\"Relevant Experience\"\r\n            value={66}\r\n            color=\"bg-purple-600\"\r\n          />\r\n          <ScoreBar label=\"Job Knowledge\" value={66} />\r\n          <ScoreBar label=\"Education\" value={66} />\r\n          <ScoreBar label=\"Hard Skills\" value={66} />\r\n        </div>\r\n\r\n        <div className=\"mt-4 font-medium flex justify-between bg-gray-100 text-sm text-center border rounded-xl p-8\">\r\n          Over All Score &nbsp; <span className=\"text-black\">66/100</span>\r\n        </div>\r\n      </div>\r\n\r\n      {/* Video Score */}\r\n      <div className=\"bg-white rounded-lg p-4 shadow-sm\">\r\n        <div className=\"font-semibold mb-4\">Video Score</div>\r\n        <div className=\"flex flex-col gap-4\">\r\n          <ScoreBar label=\"Professionalism\" value={64} />\r\n          <ScoreBar label=\"Energy Level\" value={56} color=\"bg-purple-600\" />\r\n          <ScoreBar label=\"Communication\" value={58} />\r\n          <ScoreBar label=\"Sociability\" value={70} />\r\n        </div>\r\n      </div>\r\n\r\n      {/* AI Ratings */}\r\n      <div className=\"bg-white rounded-lg p-4 flex flex-col space-y-2   gap-5 shadow-sm\">\r\n        <p className=\"font-semibold\">AI Rating</p>\r\n        <CircularRating\r\n          label=\"AI Resume Rating\"\r\n          percent={75}\r\n          color=\"#A855F7\"\r\n          trailColor=\"#EAE2FF\"\r\n        />\r\n        <CircularRating\r\n          label=\"AI Video Rating\"\r\n          percent={75}\r\n          color=\"#FF5B00\"\r\n          trailColor=\"#FFEAE1\"\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ScoreCard;\r\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEA,MAAM,YAAY;IAChB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;;;;;;;kCAER,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;0CACrC,8OAAC,mIAAA,CAAA,UAAQ;gCACP,OAAM;gCACN,OAAO;gCACP,OAAM;;;;;;0CAER,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAgB,OAAO;;;;;;0CACvC,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAY,OAAO;;;;;;0CACnC,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;;;;;;;kCAGvC,8OAAC;wBAAI,WAAU;;4BAA8F;0CACrF,8OAAC;gCAAK,WAAU;0CAAa;;;;;;;;;;;;;;;;;;0BAKvD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCAAqB;;;;;;kCACpC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAkB,OAAO;;;;;;0CACzC,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAe,OAAO;gCAAI,OAAM;;;;;;0CAChD,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAgB,OAAO;;;;;;0CACvC,8OAAC,mIAAA,CAAA,UAAQ;gCAAC,OAAM;gCAAc,OAAO;;;;;;;;;;;;;;;;;;0BAKzC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAE,WAAU;kCAAgB;;;;;;kCAC7B,8OAAC,yIAAA,CAAA,UAAc;wBACb,OAAM;wBACN,SAAS;wBACT,OAAM;wBACN,YAAW;;;;;;kCAEb,8OAAC,yIAAA,CAAA,UAAc;wBACb,OAAM;wBACN,SAAS;wBACT,OAAM;wBACN,YAAW;;;;;;;;;;;;;;;;;;AAKrB;uCAEe", "debugId": null}}, {"offset": {"line": 2145, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/components/interview/Analysis.tsx"], "sourcesContent": ["// import JobInfoCard from \"@/components/JobInfoCard\";\r\nimport QuestionsList from \"@/components/QuestionsList\";\r\nimport InterviewLayout from \"@/components/InterviewLayout\";\r\nimport VideoTranscript from \"@/components/VideoTranscript\";\r\nimport InterviewCard from \"@/components/InterviewCard\";\r\nimport ScoreCard from \"../analysis/ScoreCard\";\r\n\r\nconst Analysis = () => {\r\n  return (\r\n    <div className=\"h-screen\">\r\n      <InterviewCard />\r\n      <InterviewLayout>\r\n        <div className=\"flex flex-col lg:flex-row gap-6 lg:gap-10 justify-center items-center lg:items-start\">\r\n          <QuestionsList />\r\n          {/* <CandidateWithAgent\r\n            className=\"h-[490px]\"\r\n            useAgent={false} \r\n            candidateName=\"Jonathan\"\r\n            jobTitle=\"Insurance Agent\"\r\n          /> */}\r\n          <VideoTranscript />\r\n        </div>\r\n      </InterviewLayout>\r\n      <ScoreCard />\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default Analysis;\r\n"], "names": [], "mappings": "AAAA,sDAAsD;;;;;AACtD;AACA;AACA;AACA;AACA;;;;;;;AAEA,MAAM,WAAW;IACf,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,4HAAA,CAAA,UAAa;;;;;0BACd,8OAAC,8HAAA,CAAA,UAAe;0BACd,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4HAAA,CAAA,UAAa;;;;;sCAOd,8OAAC,8HAAA,CAAA,UAAe;;;;;;;;;;;;;;;;0BAGpB,8OAAC,oIAAA,CAAA,UAAS;;;;;;;;;;;AAGhB;uCAEe", "debugId": null}}, {"offset": {"line": 2214, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Softwares/Ai%20bot/intview-ai/app/%28root%29/interview/page.tsx"], "sourcesContent": ["\"use client\";\r\nimport React, { useState } from \"react\";\r\nimport InterviewInstructions from \"@/components/interview/InterviewInstructions\";\r\nimport InterviewWithDID from \"@/components/interview/InterviewWithDID\";\r\nimport FinishInterview from \"@/components/interview/FinishInterview\";\r\nimport Analysis from \"@/components/interview/Analysis\";\r\nimport { InterviewProvider } from \"@/context/InterviewContext\";\r\n\r\ntype InterviewStep =\r\n  | \"instructions\"\r\n  | \"questions\"\r\n  | \"recording\"\r\n  | \"finishInterview\"\r\n  | \"analysis\";\r\n\r\nconst Interview = () => {\r\n  const [currentStep, setCurrentStep] = useState<InterviewStep>(\"instructions\");\r\n\r\n  const renderCurrentComponent = () => {\r\n    switch (currentStep) {\r\n      case \"instructions\":\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"questions\")} />\r\n        );\r\n      case \"questions\":\r\n        return <InterviewWithDID onNext={() => setCurrentStep(\"finishInterview\")} />;\r\n      case \"finishInterview\":\r\n        return <FinishInterview onNext={() => setCurrentStep(\"analysis\")} />;\r\n      case \"analysis\":\r\n        return <Analysis />;\r\n      default:\r\n        return (\r\n          <InterviewInstructions onNext={() => setCurrentStep(\"questions\")} />\r\n        );\r\n    }\r\n  };\r\n\r\n  return (\r\n    <InterviewProvider>\r\n      <div>{renderCurrentComponent()}</div>\r\n    </InterviewProvider>\r\n  );\r\n};\r\n\r\nexport default Interview;\r\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;AACA;AACA;AACA;AANA;;;;;;;;AAeA,MAAM,YAAY;IAChB,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAE9D,MAAM,yBAAyB;QAC7B,OAAQ;YACN,KAAK;gBACH,qBACE,8OAAC,iJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;YAExD,KAAK;gBACH,qBAAO,8OAAC,4IAAA,CAAA,UAAgB;oBAAC,QAAQ,IAAM,eAAe;;;;;;YACxD,KAAK;gBACH,qBAAO,8OAAC,2IAAA,CAAA,UAAe;oBAAC,QAAQ,IAAM,eAAe;;;;;;YACvD,KAAK;gBACH,qBAAO,8OAAC,oIAAA,CAAA,UAAQ;;;;;YAClB;gBACE,qBACE,8OAAC,iJAAA,CAAA,UAAqB;oBAAC,QAAQ,IAAM,eAAe;;;;;;QAE1D;IACF;IAEA,qBACE,8OAAC,4HAAA,CAAA,oBAAiB;kBAChB,cAAA,8OAAC;sBAAK;;;;;;;;;;;AAGZ;uCAEe", "debugId": null}}]}