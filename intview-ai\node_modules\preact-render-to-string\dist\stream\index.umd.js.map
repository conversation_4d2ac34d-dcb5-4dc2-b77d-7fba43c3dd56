{"version": 3, "file": "index.umd.js", "sources": ["../../src/lib/util.js", "../../src/index.js", "../../src/lib/client.js", "../../src/lib/chunked.js", "../../src/stream.js"], "sourcesContent": ["export const VOID_ELEMENTS = /^(?:area|base|br|col|embed|hr|img|input|link|meta|param|source|track|wbr)$/;\nexport const UNSAFE_NAME = /[\\s\\n\\\\/='\"\\0<>]/;\nexport const NAMESPACE_REPLACE_REGEX = /^(xlink|xmlns|xml)([A-Z])/;\nexport const HTML_LOWER_CASE = /^accessK|^auto[A-Z]|^cell|^ch|^col|cont|cross|dateT|encT|form[A-Z]|frame|hrefL|inputM|maxL|minL|noV|playsI|popoverT|readO|rowS|src[A-Z]|tabI|useM|item[A-Z]/;\nexport const SVG_CAMEL_CASE = /^ac|^ali|arabic|basel|cap|clipPath$|clipRule$|color|dominant|enable|fill|flood|font|glyph[^R]|horiz|image|letter|lighting|marker[^WUH]|overline|panose|pointe|paint|rendering|shape|stop|strikethrough|stroke|text[^L]|transform|underline|unicode|units|^v[^i]|^w|^xH/;\n\n// Boolean DOM properties that translate to enumerated ('true'/'false') attributes\nexport const HTML_ENUMERATED = new Set(['draggable', 'spellcheck']);\n\n// DOM properties that should NOT have \"px\" added when numeric\nconst ENCODED_ENTITIES = /[\"&<]/;\n\n/** @param {string} str */\nexport function encodeEntities(str) {\n\t// Skip all work for strings with no entities needing encoding:\n\tif (str.length === 0 || ENCODED_ENTITIES.test(str) === false) return str;\n\n\tlet last = 0,\n\t\ti = 0,\n\t\tout = '',\n\t\tch = '';\n\n\t// Seek forward in str until the next entity char:\n\tfor (; i < str.length; i++) {\n\t\tswitch (str.charCodeAt(i)) {\n\t\t\tcase 34:\n\t\t\t\tch = '&quot;';\n\t\t\t\tbreak;\n\t\t\tcase 38:\n\t\t\t\tch = '&amp;';\n\t\t\t\tbreak;\n\t\t\tcase 60:\n\t\t\t\tch = '&lt;';\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tcontinue;\n\t\t}\n\t\t// Append skipped/buffered characters and the encoded entity:\n\t\tif (i !== last) out = out + str.slice(last, i);\n\t\tout = out + ch;\n\t\t// Start the next seek/buffer after the entity's offset:\n\t\tlast = i + 1;\n\t}\n\tif (i !== last) out = out + str.slice(last, i);\n\treturn out;\n}\n\nexport let indent = (s, char) =>\n\tString(s).replace(/(\\n+)/g, '$1' + (char || '\\t'));\n\nexport let isLargeString = (s, length, ignoreLines) =>\n\tString(s).length > (length || 40) ||\n\t(!ignoreLines && String(s).indexOf('\\n') !== -1) ||\n\tString(s).indexOf('<') !== -1;\n\nconst JS_TO_CSS = {};\n\nconst IS_NON_DIMENSIONAL = new Set([\n\t'animation-iteration-count',\n\t'border-image-outset',\n\t'border-image-slice',\n\t'border-image-width',\n\t'box-flex',\n\t'box-flex-group',\n\t'box-ordinal-group',\n\t'column-count',\n\t'fill-opacity',\n\t'flex',\n\t'flex-grow',\n\t'flex-negative',\n\t'flex-order',\n\t'flex-positive',\n\t'flex-shrink',\n\t'flood-opacity',\n\t'font-weight',\n\t'grid-column',\n\t'grid-row',\n\t'line-clamp',\n\t'line-height',\n\t'opacity',\n\t'order',\n\t'orphans',\n\t'stop-opacity',\n\t'stroke-dasharray',\n\t'stroke-dashoffset',\n\t'stroke-miterlimit',\n\t'stroke-opacity',\n\t'stroke-width',\n\t'tab-size',\n\t'widows',\n\t'z-index',\n\t'zoom'\n]);\n\nconst CSS_REGEX = /[A-Z]/g;\n// Convert an Object style to a CSSText string\nexport function styleObjToCss(s) {\n\tlet str = '';\n\tfor (let prop in s) {\n\t\tlet val = s[prop];\n\t\tif (val != null && val !== '') {\n\t\t\tconst name =\n\t\t\t\tprop[0] == '-'\n\t\t\t\t\t? prop\n\t\t\t\t\t: JS_TO_CSS[prop] ||\n\t\t\t\t\t  (JS_TO_CSS[prop] = prop.replace(CSS_REGEX, '-$&').toLowerCase());\n\n\t\t\tlet suffix = ';';\n\t\t\tif (\n\t\t\t\ttypeof val === 'number' &&\n\t\t\t\t// Exclude custom-attributes\n\t\t\t\t!name.startsWith('--') &&\n\t\t\t\t!IS_NON_DIMENSIONAL.has(name)\n\t\t\t) {\n\t\t\t\tsuffix = 'px;';\n\t\t\t}\n\t\t\tstr = str + name + ':' + val + suffix;\n\t\t}\n\t}\n\treturn str || undefined;\n}\n\n/**\n * Get flattened children from the children prop\n * @param {Array} accumulator\n * @param {any} children A `props.children` opaque object.\n * @returns {Array} accumulator\n * @private\n */\nexport function getChildren(accumulator, children) {\n\tif (Array.isArray(children)) {\n\t\tchildren.reduce(getChildren, accumulator);\n\t} else if (children != null && children !== false) {\n\t\taccumulator.push(children);\n\t}\n\treturn accumulator;\n}\n\nfunction markAsDirty() {\n\tthis.__d = true;\n}\n\nexport function createComponent(vnode, context) {\n\treturn {\n\t\t__v: vnode,\n\t\tcontext,\n\t\tprops: vnode.props,\n\t\t// silently drop state updates\n\t\tsetState: markAsDirty,\n\t\tforceUpdate: markAsDirty,\n\t\t__d: true,\n\t\t// hooks\n\t\t__h: new Array(0)\n\t};\n}\n\n// Necessary for createContext api. Setting this property will pass\n// the context value as `this.context` just for this component.\nexport function getContext(nodeName, context) {\n\tlet cxType = nodeName.contextType;\n\tlet provider = cxType && context[cxType.__c];\n\treturn cxType != null\n\t\t? provider\n\t\t\t? provider.props.value\n\t\t\t: cxType.__\n\t\t: context;\n}\n\n/**\n * @template T\n */\nexport class Deferred {\n\tconstructor() {\n\t\t// eslint-disable-next-line lines-around-comment\n\t\t/** @type {Promise<T>} */\n\t\tthis.promise = new Promise((resolve, reject) => {\n\t\t\tthis.resolve = resolve;\n\t\t\tthis.reject = reject;\n\t\t});\n\t}\n}\n", "import {\n\tencodeEntities,\n\tstyleObjToCss,\n\tUNSAFE_NAME,\n\tNAMESPACE_REPLACE_REGEX,\n\tHTML_LOWER_CASE,\n\tHTML_ENUMERATED,\n\tSVG_CAMEL_CASE,\n\tcreateComponent\n} from './lib/util.js';\nimport { options, h, Fragment } from 'preact';\nimport {\n\tCHILDREN,\n\tCOMMIT,\n\tCOMPONENT,\n\tDIFF,\n\tDIFFED,\n\tDIRTY,\n\tNEXT_STATE,\n\tPARENT,\n\tRENDER,\n\tSKIP_EFFECTS,\n\tVNODE,\n\tCATCH_ERROR\n} from './lib/constants.js';\n\nconst EMPTY_OBJ = {};\nconst EMPTY_ARR = [];\nconst isArray = Array.isArray;\nconst assign = Object.assign;\nconst EMPTY_STR = '';\n\n// Global state for the current render pass\nlet beforeDiff, afterDiff, renderHook, ummountHook;\n\n/**\n * Render Preact JSX + Components to an HTML string.\n * @param {VNode} vnode\tJSX Element / VNode to render\n * @param {Object} [context={}] Initial root context object\n * @param {RendererState} [_rendererState] for internal use\n * @returns {string} serialized HTML\n */\nexport function renderToString(vnode, context, _rendererState) {\n\t// Performance optimization: `renderToString` is synchronous and we\n\t// therefore don't execute any effects. To do that we pass an empty\n\t// array to `options._commit` (`__c`). But we can go one step further\n\t// and avoid a lot of dirty checks and allocations by setting\n\t// `options._skipEffects` (`__s`) too.\n\tconst previousSkipEffects = options[SKIP_EFFECTS];\n\toptions[SKIP_EFFECTS] = true;\n\n\t// store options hooks once before each synchronous render call\n\tbeforeDiff = options[DIFF];\n\tafterDiff = options[DIFFED];\n\trenderHook = options[RENDER];\n\tummountHook = options.unmount;\n\n\tconst parent = h(Fragment, null);\n\tparent[CHILDREN] = [vnode];\n\n\ttry {\n\t\tconst rendered = _renderToString(\n\t\t\tvnode,\n\t\t\tcontext || EMPTY_OBJ,\n\t\t\tfalse,\n\t\t\tundefined,\n\t\t\tparent,\n\t\t\tfalse,\n\t\t\t_rendererState\n\t\t);\n\n\t\tif (isArray(rendered)) {\n\t\t\treturn rendered.join(EMPTY_STR);\n\t\t}\n\t\treturn rendered;\n\t} catch (e) {\n\t\tif (e.then) {\n\t\t\tthrow new Error('Use \"renderToStringAsync\" for suspenseful rendering.');\n\t\t}\n\n\t\tthrow e;\n\t} finally {\n\t\t// options._commit, we don't schedule any effects in this library right now,\n\t\t// so we can pass an empty queue to this hook.\n\t\tif (options[COMMIT]) options[COMMIT](vnode, EMPTY_ARR);\n\t\toptions[SKIP_EFFECTS] = previousSkipEffects;\n\t\tEMPTY_ARR.length = 0;\n\t}\n}\n\n/**\n * Render Preact JSX + Components to an HTML string.\n * @param {VNode} vnode\tJSX Element / VNode to render\n * @param {Object} [context={}] Initial root context object\n * @returns {string} serialized HTML\n */\nexport async function renderToStringAsync(vnode, context) {\n\t// Performance optimization: `renderToString` is synchronous and we\n\t// therefore don't execute any effects. To do that we pass an empty\n\t// array to `options._commit` (`__c`). But we can go one step further\n\t// and avoid a lot of dirty checks and allocations by setting\n\t// `options._skipEffects` (`__s`) too.\n\tconst previousSkipEffects = options[SKIP_EFFECTS];\n\toptions[SKIP_EFFECTS] = true;\n\n\t// store options hooks once before each synchronous render call\n\tbeforeDiff = options[DIFF];\n\tafterDiff = options[DIFFED];\n\trenderHook = options[RENDER];\n\tummountHook = options.unmount;\n\n\tconst parent = h(Fragment, null);\n\tparent[CHILDREN] = [vnode];\n\n\ttry {\n\t\tconst rendered = await _renderToString(\n\t\t\tvnode,\n\t\t\tcontext || EMPTY_OBJ,\n\t\t\tfalse,\n\t\t\tundefined,\n\t\t\tparent,\n\t\t\ttrue,\n\t\t\tundefined\n\t\t);\n\n\t\tif (isArray(rendered)) {\n\t\t\tlet count = 0;\n\t\t\tlet resolved = rendered;\n\n\t\t\t// Resolving nested Promises with a maximum depth of 25\n\t\t\twhile (\n\t\t\t\tresolved.some(\n\t\t\t\t\t(element) => element && typeof element.then === 'function'\n\t\t\t\t) &&\n\t\t\t\tcount++ < 25\n\t\t\t) {\n\t\t\t\tresolved = (await Promise.all(resolved)).flat();\n\t\t\t}\n\n\t\t\treturn resolved.join(EMPTY_STR);\n\t\t}\n\n\t\treturn rendered;\n\t} finally {\n\t\t// options._commit, we don't schedule any effects in this library right now,\n\t\t// so we can pass an empty queue to this hook.\n\t\tif (options[COMMIT]) options[COMMIT](vnode, EMPTY_ARR);\n\t\toptions[SKIP_EFFECTS] = previousSkipEffects;\n\t\tEMPTY_ARR.length = 0;\n\t}\n}\n\n/**\n * @param {VNode} vnode\n * @param {Record<string, unknown>} context\n */\nfunction renderClassComponent(vnode, context) {\n\tlet type = /** @type {import(\"preact\").ComponentClass<typeof vnode.props>} */ (vnode.type);\n\n\tlet isMounting = true;\n\tlet c;\n\tif (vnode[COMPONENT]) {\n\t\tisMounting = false;\n\t\tc = vnode[COMPONENT];\n\t\tc.state = c[NEXT_STATE];\n\t} else {\n\t\tc = new type(vnode.props, context);\n\t}\n\n\tvnode[COMPONENT] = c;\n\tc[VNODE] = vnode;\n\n\tc.props = vnode.props;\n\tc.context = context;\n\t// turn off stateful re-rendering:\n\tc[DIRTY] = true;\n\n\tif (c.state == null) c.state = EMPTY_OBJ;\n\n\tif (c[NEXT_STATE] == null) {\n\t\tc[NEXT_STATE] = c.state;\n\t}\n\n\tif (type.getDerivedStateFromProps) {\n\t\tc.state = assign(\n\t\t\t{},\n\t\t\tc.state,\n\t\t\ttype.getDerivedStateFromProps(c.props, c.state)\n\t\t);\n\t} else if (isMounting && c.componentWillMount) {\n\t\tc.componentWillMount();\n\n\t\t// If the user called setState in cWM we need to flush pending,\n\t\t// state updates. This is the same behaviour in React.\n\t\tc.state = c[NEXT_STATE] !== c.state ? c[NEXT_STATE] : c.state;\n\t} else if (!isMounting && c.componentWillUpdate) {\n\t\tc.componentWillUpdate();\n\t}\n\n\tif (renderHook) renderHook(vnode);\n\n\treturn c.render(c.props, c.state, context);\n}\n\n/**\n * Recursively render VNodes to HTML.\n * @param {VNode|any} vnode\n * @param {any} context\n * @param {boolean} isSvgMode\n * @param {any} selectValue\n * @param {VNode} parent\n * @param {boolean} asyncMode\n * @param {RendererState | undefined} [renderer]\n * @returns {string | Promise<string> | (string | Promise<string>)[]}\n */\nfunction _renderToString(\n\tvnode,\n\tcontext,\n\tisSvgMode,\n\tselectValue,\n\tparent,\n\tasyncMode,\n\trenderer\n) {\n\t// Ignore non-rendered VNodes/values\n\tif (\n\t\tvnode == null ||\n\t\tvnode === true ||\n\t\tvnode === false ||\n\t\tvnode === EMPTY_STR\n\t) {\n\t\treturn EMPTY_STR;\n\t}\n\n\tlet vnodeType = typeof vnode;\n\t// Text VNodes: escape as HTML\n\tif (vnodeType != 'object') {\n\t\tif (vnodeType == 'function') return EMPTY_STR;\n\t\treturn vnodeType == 'string' ? encodeEntities(vnode) : vnode + EMPTY_STR;\n\t}\n\n\t// Recurse into children / Arrays\n\tif (isArray(vnode)) {\n\t\tlet rendered = EMPTY_STR,\n\t\t\trenderArray;\n\t\tparent[CHILDREN] = vnode;\n\t\tfor (let i = 0; i < vnode.length; i++) {\n\t\t\tlet child = vnode[i];\n\t\t\tif (child == null || typeof child == 'boolean') continue;\n\n\t\t\tconst childRender = _renderToString(\n\t\t\t\tchild,\n\t\t\t\tcontext,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue,\n\t\t\t\tparent,\n\t\t\t\tasyncMode,\n\t\t\t\trenderer\n\t\t\t);\n\n\t\t\tif (typeof childRender == 'string') {\n\t\t\t\trendered = rendered + childRender;\n\t\t\t} else {\n\t\t\t\tif (!renderArray) {\n\t\t\t\t\trenderArray = [];\n\t\t\t\t}\n\n\t\t\t\tif (rendered) renderArray.push(rendered);\n\n\t\t\t\trendered = EMPTY_STR;\n\n\t\t\t\tif (isArray(childRender)) {\n\t\t\t\t\trenderArray.push(...childRender);\n\t\t\t\t} else {\n\t\t\t\t\trenderArray.push(childRender);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\tif (renderArray) {\n\t\t\tif (rendered) renderArray.push(rendered);\n\t\t\treturn renderArray;\n\t\t}\n\n\t\treturn rendered;\n\t}\n\n\t// VNodes have {constructor:undefined} to prevent JSON injection:\n\tif (vnode.constructor !== undefined) return EMPTY_STR;\n\n\tvnode[PARENT] = parent;\n\tif (beforeDiff) beforeDiff(vnode);\n\n\tlet type = vnode.type,\n\t\tprops = vnode.props;\n\n\t// Invoke rendering on Components\n\tif (typeof type == 'function') {\n\t\tlet cctx = context,\n\t\t\tcontextType,\n\t\t\trendered,\n\t\t\tcomponent;\n\t\tif (type === Fragment) {\n\t\t\t// Serialized precompiled JSX.\n\t\t\tif ('tpl' in props) {\n\t\t\t\tlet out = EMPTY_STR;\n\t\t\t\tfor (let i = 0; i < props.tpl.length; i++) {\n\t\t\t\t\tout = out + props.tpl[i];\n\n\t\t\t\t\tif (props.exprs && i < props.exprs.length) {\n\t\t\t\t\t\tconst value = props.exprs[i];\n\t\t\t\t\t\tif (value == null) continue;\n\n\t\t\t\t\t\t// Check if we're dealing with a vnode or an array of nodes\n\t\t\t\t\t\tif (\n\t\t\t\t\t\t\ttypeof value == 'object' &&\n\t\t\t\t\t\t\t(value.constructor === undefined || isArray(value))\n\t\t\t\t\t\t) {\n\t\t\t\t\t\t\tout =\n\t\t\t\t\t\t\t\tout +\n\t\t\t\t\t\t\t\t_renderToString(\n\t\t\t\t\t\t\t\t\tvalue,\n\t\t\t\t\t\t\t\t\tcontext,\n\t\t\t\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\t\t\t\tvnode,\n\t\t\t\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\t\t\t\trenderer\n\t\t\t\t\t\t\t\t);\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\t// Values are pre-escaped by the JSX transform\n\t\t\t\t\t\t\tout = out + value;\n\t\t\t\t\t\t}\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\treturn out;\n\t\t\t} else if ('UNSTABLE_comment' in props) {\n\t\t\t\t// Fragments are the least used components of core that's why\n\t\t\t\t// branching here for comments has the least effect on perf.\n\t\t\t\treturn '<!--' + encodeEntities(props.UNSTABLE_comment) + '-->';\n\t\t\t}\n\n\t\t\trendered = props.children;\n\t\t} else {\n\t\t\tcontextType = type.contextType;\n\t\t\tif (contextType != null) {\n\t\t\t\tlet provider = context[contextType.__c];\n\t\t\t\tcctx = provider ? provider.props.value : contextType.__;\n\t\t\t}\n\n\t\t\tlet isClassComponent =\n\t\t\t\ttype.prototype && typeof type.prototype.render == 'function';\n\t\t\tif (isClassComponent) {\n\t\t\t\trendered = /**#__NOINLINE__**/ renderClassComponent(vnode, cctx);\n\t\t\t\tcomponent = vnode[COMPONENT];\n\t\t\t} else {\n\t\t\t\tvnode[COMPONENT] = component = /**#__NOINLINE__**/ createComponent(\n\t\t\t\t\tvnode,\n\t\t\t\t\tcctx\n\t\t\t\t);\n\n\t\t\t\t// If a hook invokes setState() to invalidate the component during rendering,\n\t\t\t\t// re-render it up to 25 times to allow \"settling\" of memoized states.\n\t\t\t\t// Note:\n\t\t\t\t//   This will need to be updated for Preact 11 to use internal.flags rather than component._dirty:\n\t\t\t\t//   https://github.com/preactjs/preact/blob/d4ca6fdb19bc715e49fd144e69f7296b2f4daa40/src/diff/component.js#L35-L44\n\t\t\t\tlet count = 0;\n\t\t\t\twhile (component[DIRTY] && count++ < 25) {\n\t\t\t\t\tcomponent[DIRTY] = false;\n\n\t\t\t\t\tif (renderHook) renderHook(vnode);\n\n\t\t\t\t\trendered = type.call(component, props, cctx);\n\t\t\t\t}\n\t\t\t\tcomponent[DIRTY] = true;\n\t\t\t}\n\n\t\t\tif (component.getChildContext != null) {\n\t\t\t\tcontext = assign({}, context, component.getChildContext());\n\t\t\t}\n\n\t\t\tif (\n\t\t\t\tisClassComponent &&\n\t\t\t\toptions.errorBoundaries &&\n\t\t\t\t(type.getDerivedStateFromError || component.componentDidCatch)\n\t\t\t) {\n\t\t\t\t// When a component returns a Fragment node we flatten it in core, so we\n\t\t\t\t// need to mirror that logic here too\n\t\t\t\tlet isTopLevelFragment =\n\t\t\t\t\trendered != null &&\n\t\t\t\t\trendered.type === Fragment &&\n\t\t\t\t\trendered.key == null &&\n\t\t\t\t\trendered.props.tpl == null;\n\t\t\t\trendered = isTopLevelFragment ? rendered.props.children : rendered;\n\n\t\t\t\ttry {\n\t\t\t\t\treturn _renderToString(\n\t\t\t\t\t\trendered,\n\t\t\t\t\t\tcontext,\n\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\tvnode,\n\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\trenderer\n\t\t\t\t\t);\n\t\t\t\t} catch (err) {\n\t\t\t\t\tif (type.getDerivedStateFromError) {\n\t\t\t\t\t\tcomponent[NEXT_STATE] = type.getDerivedStateFromError(err);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (component.componentDidCatch) {\n\t\t\t\t\t\tcomponent.componentDidCatch(err, EMPTY_OBJ);\n\t\t\t\t\t}\n\n\t\t\t\t\tif (component[DIRTY]) {\n\t\t\t\t\t\trendered = renderClassComponent(vnode, context);\n\t\t\t\t\t\tcomponent = vnode[COMPONENT];\n\n\t\t\t\t\t\tif (component.getChildContext != null) {\n\t\t\t\t\t\t\tcontext = assign({}, context, component.getChildContext());\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tlet isTopLevelFragment =\n\t\t\t\t\t\t\trendered != null &&\n\t\t\t\t\t\t\trendered.type === Fragment &&\n\t\t\t\t\t\t\trendered.key == null &&\n\t\t\t\t\t\t\trendered.props.tpl == null;\n\t\t\t\t\t\trendered = isTopLevelFragment ? rendered.props.children : rendered;\n\n\t\t\t\t\t\treturn _renderToString(\n\t\t\t\t\t\t\trendered,\n\t\t\t\t\t\t\tcontext,\n\t\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\t\tvnode,\n\t\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\t\trenderer\n\t\t\t\t\t\t);\n\t\t\t\t\t}\n\n\t\t\t\t\treturn EMPTY_STR;\n\t\t\t\t} finally {\n\t\t\t\t\tif (afterDiff) afterDiff(vnode);\n\t\t\t\t\tvnode[PARENT] = null;\n\n\t\t\t\t\tif (ummountHook) ummountHook(vnode);\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// When a component returns a Fragment node we flatten it in core, so we\n\t\t// need to mirror that logic here too\n\t\tlet isTopLevelFragment =\n\t\t\trendered != null &&\n\t\t\trendered.type === Fragment &&\n\t\t\trendered.key == null &&\n\t\t\trendered.props.tpl == null;\n\t\trendered = isTopLevelFragment ? rendered.props.children : rendered;\n\n\t\ttry {\n\t\t\t// Recurse into children before invoking the after-diff hook\n\t\t\tconst str = _renderToString(\n\t\t\t\trendered,\n\t\t\t\tcontext,\n\t\t\t\tisSvgMode,\n\t\t\t\tselectValue,\n\t\t\t\tvnode,\n\t\t\t\tasyncMode,\n\t\t\t\trenderer\n\t\t\t);\n\n\t\t\tif (afterDiff) afterDiff(vnode);\n\t\t\t// when we are dealing with suspense we can't do this...\n\t\t\tvnode[PARENT] = null;\n\n\t\t\tif (options.unmount) options.unmount(vnode);\n\n\t\t\treturn str;\n\t\t} catch (error) {\n\t\t\tif (!asyncMode && renderer && renderer.onError) {\n\t\t\t\tlet res = renderer.onError(error, vnode, (child) =>\n\t\t\t\t\t_renderToString(\n\t\t\t\t\t\tchild,\n\t\t\t\t\t\tcontext,\n\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\tvnode,\n\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\trenderer\n\t\t\t\t\t)\n\t\t\t\t);\n\n\t\t\t\tif (res !== undefined) return res;\n\n\t\t\t\tlet errorHook = options[CATCH_ERROR];\n\t\t\t\tif (errorHook) errorHook(error, vnode);\n\t\t\t\treturn EMPTY_STR;\n\t\t\t}\n\n\t\t\tif (!asyncMode) throw error;\n\n\t\t\tif (!error || typeof error.then != 'function') throw error;\n\n\t\t\tconst renderNestedChildren = () => {\n\t\t\t\ttry {\n\t\t\t\t\treturn _renderToString(\n\t\t\t\t\t\trendered,\n\t\t\t\t\t\tcontext,\n\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\tvnode,\n\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\trenderer\n\t\t\t\t\t);\n\t\t\t\t} catch (e) {\n\t\t\t\t\tif (!e || typeof e.then != 'function') throw e;\n\n\t\t\t\t\treturn e.then(\n\t\t\t\t\t\t() =>\n\t\t\t\t\t\t\t_renderToString(\n\t\t\t\t\t\t\t\trendered,\n\t\t\t\t\t\t\t\tcontext,\n\t\t\t\t\t\t\t\tisSvgMode,\n\t\t\t\t\t\t\t\tselectValue,\n\t\t\t\t\t\t\t\tvnode,\n\t\t\t\t\t\t\t\tasyncMode,\n\t\t\t\t\t\t\t\trenderer\n\t\t\t\t\t\t\t),\n\t\t\t\t\t\trenderNestedChildren\n\t\t\t\t\t);\n\t\t\t\t}\n\t\t\t};\n\n\t\t\treturn error.then(renderNestedChildren);\n\t\t}\n\t}\n\n\t// Serialize Element VNodes to HTML\n\tlet s = '<' + type,\n\t\thtml = EMPTY_STR,\n\t\tchildren;\n\n\tfor (let name in props) {\n\t\tlet v = props[name];\n\n\t\tif (typeof v == 'function' && name !== 'class' && name !== 'className') {\n\t\t\tcontinue;\n\t\t}\n\n\t\tswitch (name) {\n\t\t\tcase 'children':\n\t\t\t\tchildren = v;\n\t\t\t\tcontinue;\n\n\t\t\t// VDOM-specific props\n\t\t\tcase 'key':\n\t\t\tcase 'ref':\n\t\t\tcase '__self':\n\t\t\tcase '__source':\n\t\t\t\tcontinue;\n\n\t\t\t// prefer for/class over htmlFor/className\n\t\t\tcase 'htmlFor':\n\t\t\t\tif ('for' in props) continue;\n\t\t\t\tname = 'for';\n\t\t\t\tbreak;\n\t\t\tcase 'className':\n\t\t\t\tif ('class' in props) continue;\n\t\t\t\tname = 'class';\n\t\t\t\tbreak;\n\n\t\t\t// Form element reflected properties\n\t\t\tcase 'defaultChecked':\n\t\t\t\tname = 'checked';\n\t\t\t\tbreak;\n\t\t\tcase 'defaultSelected':\n\t\t\t\tname = 'selected';\n\t\t\t\tbreak;\n\n\t\t\t// Special value attribute handling\n\t\t\tcase 'defaultValue':\n\t\t\tcase 'value':\n\t\t\t\tname = 'value';\n\t\t\t\tswitch (type) {\n\t\t\t\t\t// <textarea value=\"a&b\"> --> <textarea>a&amp;b</textarea>\n\t\t\t\t\tcase 'textarea':\n\t\t\t\t\t\tchildren = v;\n\t\t\t\t\t\tcontinue;\n\n\t\t\t\t\t// <select value> is serialized as a selected attribute on the matching option child\n\t\t\t\t\tcase 'select':\n\t\t\t\t\t\tselectValue = v;\n\t\t\t\t\t\tcontinue;\n\n\t\t\t\t\t// Add a selected attribute to <option> if its value matches the parent <select> value\n\t\t\t\t\tcase 'option':\n\t\t\t\t\t\tif (selectValue == v && !('selected' in props)) {\n\t\t\t\t\t\t\ts = s + ' selected';\n\t\t\t\t\t\t}\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tbreak;\n\n\t\t\tcase 'dangerouslySetInnerHTML':\n\t\t\t\thtml = v && v.__html;\n\t\t\t\tcontinue;\n\n\t\t\t// serialize object styles to a CSS string\n\t\t\tcase 'style':\n\t\t\t\tif (typeof v === 'object') {\n\t\t\t\t\tv = styleObjToCss(v);\n\t\t\t\t}\n\t\t\t\tbreak;\n\t\t\tcase 'acceptCharset':\n\t\t\t\tname = 'accept-charset';\n\t\t\t\tbreak;\n\t\t\tcase 'httpEquiv':\n\t\t\t\tname = 'http-equiv';\n\t\t\t\tbreak;\n\n\t\t\tdefault: {\n\t\t\t\tif (NAMESPACE_REPLACE_REGEX.test(name)) {\n\t\t\t\t\tname = name.replace(NAMESPACE_REPLACE_REGEX, '$1:$2').toLowerCase();\n\t\t\t\t} else if (UNSAFE_NAME.test(name)) {\n\t\t\t\t\tcontinue;\n\t\t\t\t} else if (\n\t\t\t\t\t(name[4] === '-' || HTML_ENUMERATED.has(name)) &&\n\t\t\t\t\tv != null\n\t\t\t\t) {\n\t\t\t\t\t// serialize boolean aria-xyz or enumerated attribute values as strings\n\t\t\t\t\tv = v + EMPTY_STR;\n\t\t\t\t} else if (isSvgMode) {\n\t\t\t\t\tif (SVG_CAMEL_CASE.test(name)) {\n\t\t\t\t\t\tname =\n\t\t\t\t\t\t\tname === 'panose1'\n\t\t\t\t\t\t\t\t? 'panose-1'\n\t\t\t\t\t\t\t\t: name.replace(/([A-Z])/g, '-$1').toLowerCase();\n\t\t\t\t\t}\n\t\t\t\t} else if (HTML_LOWER_CASE.test(name)) {\n\t\t\t\t\tname = name.toLowerCase();\n\t\t\t\t}\n\t\t\t}\n\t\t}\n\n\t\t// write this attribute to the buffer\n\t\tif (v != null && v !== false) {\n\t\t\tif (v === true || v === EMPTY_STR) {\n\t\t\t\ts = s + ' ' + name;\n\t\t\t} else {\n\t\t\t\ts =\n\t\t\t\t\ts +\n\t\t\t\t\t' ' +\n\t\t\t\t\tname +\n\t\t\t\t\t'=\"' +\n\t\t\t\t\t(typeof v == 'string' ? encodeEntities(v) : v + EMPTY_STR) +\n\t\t\t\t\t'\"';\n\t\t\t}\n\t\t}\n\t}\n\n\tif (UNSAFE_NAME.test(type)) {\n\t\t// this seems to performs a lot better than throwing\n\t\t// return '<!-- -->';\n\t\tthrow new Error(`${type} is not a valid HTML tag name in ${s}>`);\n\t}\n\n\tif (html) {\n\t\t// dangerouslySetInnerHTML defined this node's contents\n\t} else if (typeof children === 'string') {\n\t\t// single text child\n\t\thtml = encodeEntities(children);\n\t} else if (children != null && children !== false && children !== true) {\n\t\t// recurse into this element VNode's children\n\t\tlet childSvgMode =\n\t\t\ttype === 'svg' || (type !== 'foreignObject' && isSvgMode);\n\t\thtml = _renderToString(\n\t\t\tchildren,\n\t\t\tcontext,\n\t\t\tchildSvgMode,\n\t\t\tselectValue,\n\t\t\tvnode,\n\t\t\tasyncMode,\n\t\t\trenderer\n\t\t);\n\t}\n\n\tif (afterDiff) afterDiff(vnode);\n\n\t// TODO: this was commented before\n\tvnode[PARENT] = null;\n\n\tif (ummountHook) ummountHook(vnode);\n\n\t// Emit self-closing tag for empty void elements:\n\tif (!html && SELF_CLOSING.has(type)) {\n\t\treturn s + '/>';\n\t}\n\n\tconst endTag = '</' + type + '>';\n\tconst startTag = s + '>';\n\n\tif (isArray(html)) return [startTag, ...html, endTag];\n\telse if (typeof html != 'string') return [startTag, html, endTag];\n\treturn startTag + html + endTag;\n}\n\nconst SELF_CLOSING = new Set([\n\t'area',\n\t'base',\n\t'br',\n\t'col',\n\t'command',\n\t'embed',\n\t'hr',\n\t'img',\n\t'input',\n\t'keygen',\n\t'link',\n\t'meta',\n\t'param',\n\t'source',\n\t'track',\n\t'wbr'\n]);\n\nexport default renderToString;\nexport const render = renderToString;\nexport const renderToStaticMarkup = renderToString;\n", "/* eslint-disable no-var, key-spacing, object-curly-spacing, prefer-arrow-callback, semi, keyword-spacing */\n\n// function initPreactIslandElement() {\n// \tclass PreactIslandElement extends HTMLElement {\n// \t\tconnectedCallback() {\n// \t\t\tvar d = this;\n// \t\t\tif (!d.isConnected) return;\n\n// \t\t\tlet i = this.getAttribute('data-target');\n// \t\t\tif (!i) return;\n\n// \t\t\tvar s,\n// \t\t\t\te,\n// \t\t\t\tc = document.createNodeIterator(document, 128);\n// \t\t\twhile (c.nextNode()) {\n// \t\t\t\tlet n = c.referenceNode;\n\n// \t\t\t\tif (n.data == 'preact-island:' + i) s = n;\n// \t\t\t\telse if (n.data == '/preact-island:' + i) e = n;\n// \t\t\t\tif (s && e) break;\n// \t\t\t}\n// \t\t\tif (s && e) {\n// \t\t\t\trequestAnimationFrame(() => {\n// \t\t\t\t\tvar p = e.previousSibling;\n// \t\t\t\t\twhile (p != s) {\n// \t\t\t\t\t\tif (!p || p == s) break;\n// \t\t\t\t\t\te.parentNode.removeChild(p);\n// \t\t\t\t\t\tp = e.previousSibling;\n// \t\t\t\t\t}\n\n// \t\t\t\t\tc = s;\n// \t\t\t\t\twhile (d.firstChild) {\n// \t\t\t\t\t\ts = d.firstChild;\n// \t\t\t\t\t\td.removeChild(s);\n// \t\t\t\t\t\tc.after(s);\n// \t\t\t\t\t\tc = s;\n// \t\t\t\t\t}\n\n// \t\t\t\t\td.parentNode.removeChild(d);\n// \t\t\t\t});\n// \t\t\t}\n// \t\t}\n// \t}\n\n// \tcustomElements.define('preact-island', PreactIslandElement);\n// }\n\n// To modify the INIT_SCRIPT, uncomment the above code, modify it, and paste it into https://try.terser.org/.\nconst INIT_SCRIPT = `class e extends HTMLElement{connectedCallback(){var e=this;if(!e.isConnected)return;let t=this.getAttribute(\"data-target\");if(t){for(var r,a,i=document.createNodeIterator(document,128);i.nextNode();){let e=i.referenceNode;if(e.data==\"preact-island:\"+t?r=e:e.data==\"/preact-island:\"+t&&(a=e),r&&a)break}r&&a&&requestAnimationFrame((()=>{for(var t=a.previousSibling;t!=r&&t&&t!=r;)a.parentNode.removeChild(t),t=a.previousSibling;for(i=r;e.firstChild;)r=e.firstChild,e.removeChild(r),i.after(r),i=r;e.parentNode.removeChild(e)}))}}}customElements.define(\"preact-island\",e);`;\n\nexport function createInitScript() {\n\treturn `<script>(function(){${INIT_SCRIPT}}())</script>`;\n}\n\n/**\n * @param {string} id\n * @param {string} content\n * @returns {string}\n */\nexport function createSubtree(id, content) {\n\treturn `<preact-island hidden data-target=\"${id}\">${content}</preact-island>`;\n}\n", "import { renderToString } from '../index.js';\nimport { CHILD_DID_SUSPEND, COMPONENT, PARENT } from './constants.js';\nimport { Deferred } from './util.js';\nimport { createInitScript, createSubtree } from './client.js';\n\n/**\n * @param {VNode} vnode\n * @param {RenderToChunksOptions} options\n * @returns {Promise<void>}\n */\nexport async function renderToChunks(vnode, { context, onWrite, abortSignal }) {\n\tcontext = context || {};\n\n\t/** @type {RendererState} */\n\tconst renderer = {\n\t\tstart: Date.now(),\n\t\tabortSignal,\n\t\tonWrite,\n\t\tonError: handleError,\n\t\tsuspended: []\n\t};\n\n\t// Synchronously render the shell\n\t// @ts-ignore - using third internal RendererState argument\n\tconst shell = renderToString(vnode, context, renderer);\n\tonWrite(shell);\n\n\t// Wait for any suspended sub-trees if there are any\n\tconst len = renderer.suspended.length;\n\tif (len > 0) {\n\t\tonWrite('<div hidden>');\n\t\tonWrite(createInitScript(len));\n\t\t// We should keep checking all promises\n\t\tawait forkPromises(renderer);\n\t\tonWrite('</div>');\n\t}\n}\n\nasync function forkPromises(renderer) {\n\tif (renderer.suspended.length > 0) {\n\t\tconst suspensions = [...renderer.suspended];\n\t\tawait Promise.all(renderer.suspended.map((s) => s.promise));\n\t\trenderer.suspended = renderer.suspended.filter(\n\t\t\t(s) => !suspensions.includes(s)\n\t\t);\n\t\tawait forkPromises(renderer);\n\t}\n}\n\n/** @type {RendererErrorHandler} */\nfunction handleError(error, vnode, renderChild) {\n\tif (!error || !error.then) return;\n\n\t// walk up to the Suspense boundary\n\twhile ((vnode = vnode[PARENT])) {\n\t\tlet component = vnode[COMPONENT];\n\t\tif (component && component[CHILD_DID_SUSPEND]) {\n\t\t\tbreak;\n\t\t}\n\t}\n\n\tif (!vnode) return;\n\n\tconst id = vnode.__v;\n\tconst found = this.suspended.find((x) => x.id === id);\n\tconst race = new Deferred();\n\n\tconst abortSignal = this.abortSignal;\n\tif (abortSignal) {\n\t\t// @ts-ignore 2554 - implicit undefined arg\n\t\tif (abortSignal.aborted) race.resolve();\n\t\telse abortSignal.addEventListener('abort', race.resolve);\n\t}\n\n\tconst promise = error.then(\n\t\t() => {\n\t\t\tif (abortSignal && abortSignal.aborted) return;\n\t\t\tconst child = renderChild(vnode.props.children);\n\t\t\tif (child) this.onWrite(createSubtree(id, child));\n\t\t},\n\t\t// TODO: Abort and send hydration code snippet to client\n\t\t// to attempt to recover during hydration\n\t\tthis.onError\n\t);\n\n\tthis.suspended.push({\n\t\tid,\n\t\tvnode,\n\t\tpromise: Promise.race([promise, race.promise])\n\t});\n\n\tconst fallback = renderChild(vnode.props.fallback);\n\n\treturn found\n\t\t? ''\n\t\t: `<!--preact-island:${id}-->${fallback}<!--/preact-island:${id}-->`;\n}\n", "import { Deferred } from './lib/util.js';\nimport { renderToChunks } from './lib/chunked.js';\n\n/** @typedef {ReadableStream<Uint8Array> & { allReady: Promise<void>}} RenderStream */\n\n/**\n * @param {import('preact').VNode} vnode\n * @param {any} [context]\n * @returns {RenderStream}\n */\nexport function renderToReadableStream(vnode, context) {\n\t/** @type {Deferred<void>} */\n\tconst allReady = new Deferred();\n\tconst encoder = new TextEncoder('utf-8');\n\n\t/** @type {RenderStream} */\n\tconst stream = new ReadableStream({\n\t\tstart(controller) {\n\t\t\trenderToChunks(vnode, {\n\t\t\t\tcontext,\n\t\t\t\tonError: (error) => {\n\t\t\t\t\tallReady.reject(error);\n\t\t\t\t\tcontroller.abort(error);\n\t\t\t\t},\n\t\t\t\tonWrite(s) {\n\t\t\t\t\tcontroller.enqueue(encoder.encode(s));\n\t\t\t\t}\n\t\t\t})\n\t\t\t\t.then(() => {\n\t\t\t\t\tcontroller.close();\n\t\t\t\t\tallReady.resolve();\n\t\t\t\t})\n\t\t\t\t.catch((error) => {\n\t\t\t\t\tcontroller.error(error);\n\t\t\t\t\tallReady.reject(error);\n\t\t\t\t});\n\t\t}\n\t});\n\n\tstream.allReady = allReady.promise;\n\n\treturn stream;\n}\n"], "names": ["UNSAFE_NAME", "NAMESPACE_REPLACE_REGEX", "HTML_LOWER_CASE", "SVG_CAMEL_CASE", "HTML_ENUMERATED", "Set", "ENCODED_ENTITIES", "encodeEntities", "str", "length", "test", "last", "i", "out", "ch", "charCodeAt", "slice", "JS_TO_CSS", "IS_NON_DIMENSIONAL", "CSS_REGEX", "styleObjToCss", "s", "prop", "val", "name", "replace", "toLowerCase", "suffix", "startsWith", "has", "undefined", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "this", "__d", "createComponent", "vnode", "context", "__v", "props", "setState", "forceUpdate", "__h", "Array", "beforeDiff", "afterDiff", "renderHook", "ummountHook", "Deferred", "promise", "Promise", "resolve", "reject", "_this", "EMPTY_OBJ", "EMPTY_ARR", "isArray", "assign", "Object", "EMPTY_STR", "renderToString", "_rendererState", "previousSkipEffects", "options", "unmount", "parent", "h", "Fragment", "rendered", "_renderToString", "join", "e", "then", "Error", "renderClassComponent", "c", "type", "isMounting", "state", "getDerivedStateFromProps", "componentWillMount", "componentWillUpdate", "render", "isSvgMode", "selectValue", "asyncMode", "renderer", "vnodeType", "renderArray", "child", "childRender", "push", "constructor", "contextType", "component", "cctx", "tpl", "exprs", "value", "UNSTABLE_comment", "children", "provider", "__c", "__", "isClassComponent", "prototype", "count", "call", "getChildContext", "errorBoundaries", "getDerivedStateFromError", "componentDidCatch", "key", "err", "error", "onError", "res", "errorHook", "renderNestedChildren", "html", "v", "__html", "SELF_CLOSING", "endTag", "startTag", "createSubtree", "id", "content", "forkPromises", "suspended", "suspensions", "all", "map", "filter", "includes", "renderToChunks", "onWrite", "abortSignal", "start", "Date", "now", "handleError", "shell", "len", "<PERSON><PERSON><PERSON><PERSON>", "found", "find", "x", "race", "aborted", "addEventListener", "fallback", "allReady", "encoder", "TextEncoder", "stream", "ReadableStream", "controller", "abort", "enqueue", "encode", "close"], "mappings": "wRACaA,EAAc,mBACdC,EAA0B,4BAC1BC,EAAkB,8JAClBC,EAAiB,yQAGjBC,EAAkB,IAAIC,IAAI,CAAC,YAAa,eAG/CC,EAAmB,iBAGTC,EAAeC,GAE9B,GAAmB,IAAfA,EAAIC,SAA+C,IAA/BH,EAAiBI,KAAKF,GAAgB,OAAOA,EAQrE,IANA,IAAIG,EAAO,EACVC,EAAI,EACJC,EAAM,GACNC,EAAK,GAGCF,EAAIJ,EAAIC,OAAQG,IAAK,CAC3B,OAAQJ,EAAIO,WAAWH,IACtB,QACCE,EAAK,SACL,MACD,QACCA,EAAK,QACL,MACD,QACCA,EAAK,OACL,MACD,QACC,SAGEF,IAAMD,IAAME,GAAYL,EAAIQ,MAAML,EAAMC,IAC5CC,GAAYC,EAEZH,EAAOC,EAAI,CACX,CAED,OADIA,IAAMD,IAAME,GAAYL,EAAIQ,MAAML,EAAMC,IACrCC,CACP,CAUD,IAAMI,EAAY,GAEZC,EAAqB,IAAIb,IAAI,CAClC,4BACA,sBACA,qBACA,qBACA,WACA,iBACA,oBACA,eACA,eACA,OACA,YACA,gBACA,aACA,gBACA,cACA,gBACA,cACA,cACA,WACA,aACA,cACA,UACA,QACA,UACA,eACA,mBACA,oBACA,oBACA,iBACA,eACA,WACA,SACA,UACA,SAGKc,EAAY,kBAEFC,EAAcC,GAC7B,IAAIb,EAAM,GACV,IAAK,IAAIc,KAAQD,EAAG,CACnB,IAAIE,EAAMF,EAAEC,GACZ,GAAW,MAAPC,GAAuB,KAARA,EAAY,CAC9B,IAAMC,EACM,KAAXF,EAAK,GACFA,EACAL,EAAUK,KACTL,EAAUK,GAAQA,EAAKG,QAAQN,EAAW,OAAOO,eAElDC,EAAS,IAEG,iBAARJ,GAENC,EAAKI,WAAW,OAChBV,EAAmBW,IAAIL,KAExBG,EAAS,OAEVnB,EAAMA,EAAMgB,EAAO,IAAMD,EAAMI,CAC/B,CACD,CACD,OAAOnB,QAAOsB,CACd,CAkBD,SAASC,IACRC,KAAKC,KAAM,CACX,UAEeC,EAAgBC,EAAOC,GACtC,MAAO,CACNC,IAAKF,EACLC,QAAAA,EACAE,MAAOH,EAAMG,MAEbC,SAAUR,EACVS,YAAaT,EACbE,KAAK,EAELQ,IAAK,IAAIC,MAAM,GAEhB,KCzHGC,EAAYC,EAAWC,EAAYC,ED0I1BC,EACZ,sBAGCf,KAAKgB,QAAU,IAAIC,QAAQ,SAACC,EAASC,GACpCC,EAAKF,QAAUA,EACfE,EAAKD,OAASA,CACd,EACD,ECzJIE,EAAY,GACZC,EAAY,GACZC,EAAUb,MAAMa,QAChBC,EAASC,OAAOD,OAChBE,EAAY,YAYFC,EAAexB,EAAOC,EAASwB,GAM9C,IAAMC,EAAsBC,UAAO,IACnCA,UAAO,KAAiB,EAGxBnB,EAAamB,UAAO,IACpBlB,EAAYkB,UAAO,OACnBjB,EAAaiB,UAAO,IACpBhB,EAAcgB,UAAQC,QAEtB,IAAMC,EAASC,IAAEC,WAAU,MAC3BF,EAAM,IAAa,CAAC7B,GAEpB,IACC,IAAMgC,EAAWC,EAChBjC,EACAC,GAAWiB,GACX,OACAvB,EACAkC,GACA,EACAJ,GAGD,OAAIL,EAAQY,GACJA,EAASE,KAAKX,GAEfS,CAaP,CAZC,MAAOG,GACR,GAAIA,EAAEC,KACL,UAAUC,MAAM,wDAGjB,MAAMF,CACN,CArBD,QAwBKR,UAAO,KAAUA,UAAO,IAAS3B,EAAOmB,GAC5CQ,UAAO,IAAiBD,EACxBP,EAAU7C,OAAS,CACnB,CACD,CAoED,SAASgE,EAAqBtC,EAAOC,GACpC,IAGIsC,EAHAC,EAA2ExC,EAAMwC,KAEjFC,GAAa,EA0CjB,OAxCIzC,EAAK,KACRyC,GAAa,GACbF,EAAIvC,EAAK,KACP0C,MAAQH,EAAC,KAEXA,EAAI,IAAIC,EAAKxC,EAAMG,MAAOF,GAG3BD,EAAK,IAAcuC,EACnBA,EAAC,IAAUvC,EAEXuC,EAAEpC,MAAQH,EAAMG,MAChBoC,EAAEtC,QAAUA,EAEZsC,EAAC,KAAU,EAEI,MAAXA,EAAEG,QAAeH,EAAEG,MAAQxB,GAEV,MAAjBqB,EAAC,MACJA,EAAC,IAAeA,EAAEG,OAGfF,EAAKG,yBACRJ,EAAEG,MAAQrB,EACT,GACAkB,EAAEG,MACFF,EAAKG,yBAAyBJ,EAAEpC,MAAOoC,EAAEG,QAEhCD,GAAcF,EAAEK,oBAC1BL,EAAEK,qBAIFL,EAAEG,MAAQH,EAAC,MAAiBA,EAAEG,MAAQH,EAAC,IAAeA,EAAEG,QAC7CD,GAAcF,EAAEM,qBAC3BN,EAAEM,sBAGCnC,GAAYA,EAAWV,GAEpBuC,EAAEO,OAAOP,EAAEpC,MAAOoC,EAAEG,MAAOzC,EAClC,CAaD,SAASgC,EACRjC,EACAC,EACA8C,EACAC,EACAnB,EACAoB,EACAC,GAGA,GACU,MAATlD,IACU,IAAVA,IACU,IAAVA,GACAA,IAAUuB,EAEV,OAAOA,EAGR,IAAI4B,SAAmBnD,EAEvB,GAAiB,UAAbmD,EACH,MAAiB,YAAbA,EAAgC5B,EAChB,UAAb4B,EAAwB/E,EAAe4B,GAASA,EAAQuB,EAIhE,GAAIH,EAAQpB,GAAQ,CACnB,IACCoD,EADGpB,EAAWT,EAEfM,EAAM,IAAa7B,EACnB,IAAK,IAAIvB,EAAI,EAAGA,EAAIuB,EAAM1B,OAAQG,IAAK,CACtC,IAAI4E,EAAQrD,EAAMvB,GAClB,GAAa,MAAT4E,GAAiC,kBAATA,EAA5B,CAEA,MAAMC,EAAcrB,EACnBoB,EACApD,EACA8C,EACAC,EACAnB,EACAoB,EACAC,GAGyB,iBAAfI,EACVtB,GAAsBsB,GAEjBF,IACJA,EAAc,IAGXpB,GAAUoB,EAAYG,KAAKvB,GAE/BA,EAAWT,EAEPH,EAAQkC,MACXF,GAAYG,aAAQD,GAEpBF,EAAYG,KAAKD,IAGnB,CAED,OAAIF,GACCpB,GAAUoB,EAAYG,KAAKvB,GACxBoB,GAGDpB,CACP,CAGD,QAA0BrC,IAAtBK,EAAMwD,YAA2B,OAAOjC,EAE5CvB,EAAK,GAAW6B,EACZrB,GAAYA,EAAWR,GAE3B,IAAIwC,EAAOxC,EAAMwC,KAChBrC,EAAQH,EAAMG,MAGf,GAAmB,mBAARqC,EAAoB,CAC9B,IACCiB,EACAzB,EACA0B,EAHGC,EAAO1D,EAIX,GAAIuC,IAAST,WAAU,CAEtB,GAAI,QAAS5B,EAAO,CAEnB,IADA,IAAIzB,EAAM6C,EACD9C,EAAI,EAAGA,EAAI0B,EAAMyD,IAAItF,OAAQG,IAGrC,GAFAC,GAAYyB,EAAMyD,IAAInF,GAElB0B,EAAM0D,OAASpF,EAAI0B,EAAM0D,MAAMvF,OAAQ,CAC1C,IAAMwF,EAAQ3D,EAAM0D,MAAMpF,GAC1B,GAAa,MAATqF,EAAe,SAIF,iBAATA,QACgBnE,IAAtBmE,EAAMN,cAA6BpC,EAAQ0C,GAe5CpF,GAAYoF,EAbZpF,GAECuD,EACC6B,EACA7D,EACA8C,EACAC,EACAhD,EACAiD,EACAC,EAMH,CAGF,OAAOxE,CACP,IAAU,qBAAsByB,EAGhC,MAAO,UAAS/B,EAAe+B,EAAM4D,kBAAoB,SAG1D/B,EAAW7B,EAAM6D,QACjB,KAAM,CAEN,GAAmB,OADnBP,EAAcjB,EAAKiB,aACM,CACxB,IAAIQ,EAAWhE,EAAQwD,EAAYS,KACnCP,EAAOM,EAAWA,EAAS9D,MAAM2D,MAAQL,EAAYU,EACrD,CAED,IAAIC,EACH5B,EAAK6B,WAA6C,mBAAzB7B,EAAK6B,UAAUvB,OACzC,GAAIsB,EACHpC,EAA+BM,EAAqBtC,EAAO2D,GAC3DD,EAAY1D,EAAK,QACX,CACNA,EAAK,IAAc0D,EAAgC3D,EAClDC,EACA2D,GASD,IADA,IAAIW,EAAQ,EACLZ,EAAS,KAAWY,IAAU,IACpCZ,EAAS,KAAU,EAEfhD,GAAYA,EAAWV,GAE3BgC,EAAWQ,EAAK+B,KAAKb,EAAWvD,EAAOwD,GAExCD,EAAS,KAAU,CACnB,CAMD,GAJiC,MAA7BA,EAAUc,kBACbvE,EAAUoB,EAAO,GAAIpB,EAASyD,EAAUc,oBAIxCJ,GACAzC,UAAQ8C,kBACPjC,EAAKkC,0BAA4BhB,EAAUiB,mBAC3C,CAQD3C,EAJa,MAAZA,GACAA,EAASQ,OAAST,YACF,MAAhBC,EAAS4C,KACa,MAAtB5C,EAAS7B,MAAMyD,IACgB5B,EAAS7B,MAAM6D,SAAWhC,EAE1D,IACC,OAAOC,EACND,EACA/B,EACA8C,EACAC,EACAhD,EACAiD,EACAC,EA2CD,CAzCC,MAAO2B,GASR,OARIrC,EAAKkC,2BACRhB,EAAS,IAAelB,EAAKkC,yBAAyBG,IAGnDnB,EAAUiB,mBACbjB,EAAUiB,kBAAkBE,EAAK3D,GAG9BwC,EAAS,KACZ1B,EAAWM,EAAqBtC,EAAOC,GAGN,OAFjCyD,EAAY1D,EAAK,KAEHwE,kBACbvE,EAAUoB,EAAO,GAAIpB,EAASyD,EAAUc,oBAUlCvC,EAFPD,EAJa,MAAZA,GACAA,EAASQ,OAAST,YACF,MAAhBC,EAAS4C,KACa,MAAtB5C,EAAS7B,MAAMyD,IACgB5B,EAAS7B,MAAM6D,SAAWhC,EAIzD/B,EACA8C,EACAC,EACAhD,EACAiD,EACAC,IAIK3B,CACP,CA9CD,QA+CKd,GAAWA,EAAUT,GACzBA,EAAK,GAAW,KAEZW,GAAaA,EAAYX,EAC7B,CACD,CACD,CASDgC,EAJa,MAAZA,GACAA,EAASQ,OAAST,YACF,MAAhBC,EAAS4C,KACa,MAAtB5C,EAAS7B,MAAMyD,IACgB5B,EAAS7B,MAAM6D,SAAWhC,EAE1D,IAEC,IAAM3D,EAAM4D,EACXD,EACA/B,EACA8C,EACAC,EACAhD,EACAiD,EACAC,GASD,OANIzC,GAAWA,EAAUT,GAEzBA,EAAK,GAAW,KAEZ2B,UAAQC,SAASD,UAAQC,QAAQ5B,GAE9B3B,CAyDP,CAxDC,MAAOyG,GACR,IAAK7B,GAAaC,GAAYA,EAAS6B,QAAS,CAC/C,IAAIC,EAAM9B,EAAS6B,QAAQD,EAAO9E,EAAO,SAACqD,UACzCpB,EACCoB,EACApD,EACA8C,EACAC,EACAhD,EACAiD,EACAC,EARuC,GAYzC,QAAYvD,IAARqF,EAAmB,OAAOA,EAE9B,IAAIC,EAAYtD,UAAO,IAEvB,OADIsD,GAAWA,EAAUH,EAAO9E,GACzBuB,CACP,CAED,IAAK0B,EAAW,MAAM6B,EAEtB,IAAKA,GAA8B,mBAAdA,EAAM1C,KAAoB,MAAM0C,EAgCrD,OAAOA,EAAM1C,KA9BgB,SAAvB8C,IACL,IACC,OAAOjD,EACND,EACA/B,EACA8C,EACAC,EACAhD,EACAiD,EACAC,EAkBD,CAhBC,MAAOf,GACR,IAAKA,GAAsB,mBAAVA,EAAEC,KAAoB,MAAMD,EAE7C,OAAOA,EAAEC,KACR,kBACCH,EACCD,EACA/B,EACA8C,EACAC,EACAhD,EACAiD,EACAC,EARF,EAUAgC,EAED,CACD,EAGD,CACD,CAGD,IAEClB,EAFG9E,EAAI,IAAMsD,EACb2C,EAAO5D,EAGR,IAAK,IAAIlC,KAAQc,EAAO,CACvB,IAAIiF,EAAIjF,EAAMd,GAEd,GAAgB,mBAAL+F,GAA4B,UAAT/F,GAA6B,cAATA,EAAlD,CAIA,OAAQA,GACP,IAAK,WACJ2E,EAAWoB,EACX,SAGD,IAAK,MACL,IAAK,MACL,IAAK,SACL,IAAK,WACJ,SAGD,IAAK,UACJ,GAAI,QAASjF,EAAO,SACpBd,EAAO,MACP,MACD,IAAK,YACJ,GAAI,UAAWc,EAAO,SACtBd,EAAO,QACP,MAGD,IAAK,iBACJA,EAAO,UACP,MACD,IAAK,kBACJA,EAAO,WACP,MAGD,IAAK,eACL,IAAK,QAEJ,OADAA,EAAO,QACCmD,GAEP,IAAK,WACJwB,EAAWoB,EACX,SAGD,IAAK,SACJpC,EAAcoC,EACd,SAGD,IAAK,SACApC,GAAeoC,GAAO,aAAcjF,IACvCjB,GAAQ,aAIX,MAED,IAAK,0BACJiG,EAAOC,GAAKA,EAAEC,OACd,SAGD,IAAK,QACa,iBAAND,IACVA,EAAInG,EAAcmG,IAEnB,MACD,IAAK,gBACJ/F,EAAO,iBACP,MACD,IAAK,YACJA,EAAO,aACP,MAED,QACC,GAAIvB,EAAwBS,KAAKc,GAChCA,EAAOA,EAAKC,QAAQxB,EAAyB,SAASyB,sBAC5C1B,EAAYU,KAAKc,GAC3B,SAEa,MAAZA,EAAK,KAAcpB,EAAgByB,IAAIL,IACnC,MAAL+F,EAIUrC,EACN/E,EAAeO,KAAKc,KACvBA,EACU,YAATA,EACG,WACAA,EAAKC,QAAQ,WAAY,OAAOC,eAE3BxB,EAAgBQ,KAAKc,KAC/BA,EAAOA,EAAKE,eATZ6F,GAAQ7D,CAUR,EAKM,MAAL6D,IAAmB,IAANA,IAEflG,GADS,IAANkG,GAAcA,IAAM7D,EACnBrC,EAAI,IAAMG,EAGbH,EACA,IACAG,EACA,MACa,iBAAL+F,EAAgBhH,EAAegH,GAAKA,EAAI7D,GAChD,IA5GF,CA+GD,CAED,GAAI1D,EAAYU,KAAKiE,GAGpB,UAAUH,MAASG,sCAAwCtD,OA+B5D,GA5BIiG,IAE2B,iBAAbnB,EAEjBmB,EAAO/G,EAAe4F,GACA,MAAZA,IAAiC,IAAbA,IAAmC,IAAbA,IAIpDmB,EAAOlD,EACN+B,EACA/D,EAHS,QAATuC,GAA4B,kBAATA,GAA4BO,EAK/CC,EACAhD,EACAiD,EACAC,KAIEzC,GAAWA,EAAUT,GAGzBA,EAAK,GAAW,KAEZW,GAAaA,EAAYX,IAGxBmF,GAAQG,EAAa5F,IAAI8C,GAC7B,OAAOtD,EAAI,KAGZ,IAAMqG,GAAS,KAAO/C,EAAO,IACvBgD,GAAWtG,EAAI,IAErB,OAAIkC,EAAQ+D,IAAeK,WAAaL,GAAMI,KACtB,iBAARJ,EAAyB,CAACK,GAAUL,EAAMI,IACnDC,GAAWL,EAAOI,EACzB,CAED,IAAMD,EAAe,IAAIpH,IAAI,CAC5B,OACA,OACA,KACA,MACA,UACA,QACA,KACA,MACA,QACA,SACA,OACA,OACA,QACA,SACA,QACA,iBCxpBeuH,EAAcC,EAAIC,GACjC,4CAA6CD,OAAOC,oBACpD,KCvBcC,WAAAA,EAAa1C,2BACvBA,EAAS2C,UAAUvH,OAAS,GAC/B,IAAMwH,YAAkB5C,EAAS2C,WAFG,uBAG9B/E,QAAQiF,IAAI7C,EAAS2C,UAAUG,IAAI,SAAC9G,UAAMA,EAAE2B,OAAT,qBAHL,OAIpCqC,EAAS2C,UAAY3C,EAAS2C,UAAUI,OACvC,SAAC/G,UAAO4G,EAAYI,SAAShH,EAA7B,mBAEK0G,EAAa1C,+HAnCCiD,WAAenG,SAASC,IAAAA,QAASmG,IAAAA,QAASC,IAAAA,gBAC/DpG,EAAUA,GAAW,GAGrB,IAAMiD,EAAW,CAChBoD,MAAOC,KAAKC,MACZH,YAAAA,EACAD,QAAAA,EACArB,QAAS0B,EACTZ,UAAW,IAKNa,EAAQlF,EAAexB,EAAOC,EAASiD,GAC7CkD,EAAQM,GAGR,IAAMC,EAAMzD,EAAS2C,UAAUvH,uBAC3BqI,EAAM,EAnBoE,OAoB7EP,EAAQ,gBACRA,knBAEMR,EAAa1C,oBACnBkD,EAAQ,SAxBqE,oEAA/E,oCAwCA,SAASK,EAAY3B,EAAO9E,EAAO4G,cAClC,GAAK9B,GAAUA,EAAM1C,KAArB,CAGA,KAAQpC,EAAQA,EAAK,IAAW,CAC/B,IAAI0D,EAAY1D,EAAK,IACrB,GAAI0D,GAAaA,EAAS,IACzB,KAED,CAED,GAAK1D,EAAL,CAEA,IAAM0F,EAAK1F,EAAME,IACX2G,EAAQhH,KAAKgG,UAAUiB,KAAK,SAACC,UAAMA,EAAErB,KAAOA,CAAhB,GAC5BsB,EAAO,IAAIpG,EAEXyF,EAAcxG,KAAKwG,YACrBA,IAECA,EAAYY,QAASD,EAAKjG,UACzBsF,EAAYa,iBAAiB,QAASF,EAAKjG,UAGjD,IAAMF,EAAUiE,EAAM1C,KACrB,WACC,IAAIiE,IAAeA,EAAYY,QAA/B,CACA,IAAM5D,EAAQuD,EAAY5G,EAAMG,MAAM6D,UAClCX,GAAOpC,EAAKmF,QAAQX,EAAcC,EAAIrC,IAC1C,EAGDxD,KAAKkF,SAGNlF,KAAKgG,UAAUtC,KAAK,CACnBmC,GAAAA,EACA1F,MAAAA,EACAa,QAASC,QAAQkG,KAAK,CAACnG,EAASmG,EAAKnG,YAGtC,IAAMsG,EAAWP,EAAY5G,EAAMG,MAAMgH,UAEzC,OAAON,EACJ,2BACqBnB,WAAQyB,2BAA8BzB,YAC9D,mCCtFsC1F,EAAOC,GAE7C,IAAMmH,EAAW,IAAIxG,EACfyG,EAAU,IAAIC,YAAY,SAG1BC,EAAS,IAAIC,eAAe,CACjClB,eAAMmB,GACLtB,EAAenG,EAAO,CACrBC,QAAAA,EACA8E,QAAS,SAACD,GACTsC,EAASpG,OAAO8D,GAChB2C,EAAWC,MAAM5C,EACjB,EACDsB,iBAAQlH,GACPuI,EAAWE,QAAQN,EAAQO,OAAO1I,GAClC,IAEAkD,KAAK,WACLqF,EAAWI,QACXT,EAASrG,SACT,SACM,SAAC+D,GACP2C,EAAW3C,MAAMA,GACjBsC,EAASpG,OAAO8D,EAChB,EACF,IAKF,OAFAyC,EAAOH,SAAWA,EAASvG,QAEpB0G,CACP"}