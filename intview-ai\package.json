{"name": "hire-ai", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-slot": "^1.2.3", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "eslint-config-prettier": "^10.1.5", "eslint-config-standard": "^17.1.0", "eslint-plugin-n": "^17.21.0", "eslint-plugin-promise": "^7.2.1", "eslint-plugin-tailwindcss": "^3.18.0", "framer-motion": "^12.23.12", "lucide-react": "^0.525.0", "next": "15.3.5", "next-auth": "^5.0.0-beta.29", "next-themes": "^0.4.6", "prettier": "^3.6.2", "react": "^19.0.0", "react-circular-progressbar": "^2.2.0", "react-dom": "^19.0.0", "react-hook-form": "^7.61.1", "react-icons": "^5.5.0", "react-speech-recognition": "^3.10.0", "sonner": "^2.0.6", "tailwind-merge": "^3.3.1", "zod": "^4.0.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/react-speech-recognition": "^3.9.6", "eslint": "^9", "eslint-config-next": "15.3.5", "eslint-plugin-import": "^2.32.0", "tailwindcss": "^4", "tw-animate-css": "^1.3.5", "typescript": "^5"}, "packageManager": "npm@10.7.0", "overrides": {"react": "$react", "react-dom": "$react-dom", "eslint": "$eslint", "eslint-config-standard": "$eslint-config-standard", "eslint-plugin-n": "$eslint-plugin-n", "eslint-plugin-promise": "$eslint-plugin-promise", "eslint-plugin-tailwindcss": "$eslint-plugin-tailwindcss", "tailwindcss": "$tailwindcss"}}