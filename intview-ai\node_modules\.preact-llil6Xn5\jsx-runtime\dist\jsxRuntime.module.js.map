{"version": 3, "file": "jsxRuntime.module.js", "sources": ["../src/utils.js", "../../src/constants.js", "../src/index.js"], "sourcesContent": ["const ENCODED_ENTITIES = /[\"&<]/;\n\n/** @param {string} str */\nexport function encodeEntities(str) {\n\t// Skip all work for strings with no entities needing encoding:\n\tif (str.length === 0 || ENCODED_ENTITIES.test(str) === false) return str;\n\n\tlet last = 0,\n\t\ti = 0,\n\t\tout = '',\n\t\tch = '';\n\n\t// Seek forward in str until the next entity char:\n\tfor (; i < str.length; i++) {\n\t\tswitch (str.charCodeAt(i)) {\n\t\t\tcase 34:\n\t\t\t\tch = '&quot;';\n\t\t\t\tbreak;\n\t\t\tcase 38:\n\t\t\t\tch = '&amp;';\n\t\t\t\tbreak;\n\t\t\tcase 60:\n\t\t\t\tch = '&lt;';\n\t\t\t\tbreak;\n\t\t\tdefault:\n\t\t\t\tcontinue;\n\t\t}\n\t\t// Append skipped/buffered characters and the encoded entity:\n\t\tif (i !== last) out += str.slice(last, i);\n\t\tout += ch;\n\t\t// Start the next seek/buffer after the entity's offset:\n\t\tlast = i + 1;\n\t}\n\tif (i !== last) out += str.slice(last, i);\n\treturn out;\n}\n", "/** Normal hydration that attaches to a DOM tree but does not diff it. */\nexport const MODE_HYDRATE = 1 << 5;\n/** Signifies this VNode suspended on the previous render */\nexport const MODE_SUSPENDED = 1 << 7;\n/** Indicates that this node needs to be inserted while patching children */\nexport const INSERT_VNODE = 1 << 16;\n/** Indicates a VNode has been matched with another VNode in the diff */\nexport const MATCHED = 1 << 17;\n\n/** Reset all mode flags */\nexport const RESET_MODE = ~(MODE_HYDRATE | MODE_SUSPENDED);\n\nexport const EMPTY_OBJ = /** @type {any} */ ({});\nexport const EMPTY_ARR = [];\nexport const IS_NON_DIMENSIONAL =\n\t/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i;\n", "import { options, Fragment } from 'preact';\nimport { encodeEntities } from './utils';\nimport { IS_NON_DIMENSIONAL } from '../../src/constants';\n\nlet vnodeId = 0;\n\nconst isArray = Array.isArray;\n\n/**\n * @fileoverview\n * This file exports various methods that implement Babel's \"automatic\" JSX runtime API:\n * - jsx(type, props, key)\n * - jsxs(type, props, key)\n * - jsxDEV(type, props, key, __source, __self)\n *\n * The implementation of createVNode here is optimized for performance.\n * Benchmarks: https://esbench.com/bench/5f6b54a0b4632100a7dcd2b3\n */\n\n/**\n * JSX.Element factory used by Babel's {runtime:\"automatic\"} JSX transform\n * @param {VNode['type']} type\n * @param {VNode['props']} props\n * @param {VNode['key']} [key]\n * @param {unknown} [isStaticChildren]\n * @param {unknown} [__source]\n * @param {unknown} [__self]\n */\nfunction createVNode(type, props, key, isStaticChildren, __source, __self) {\n\tif (!props) props = {};\n\t// We'll want to preserve `ref` in props to get rid of the need for\n\t// forwardRef components in the future, but that should happen via\n\t// a separate PR.\n\tlet normalizedProps = props,\n\t\tref,\n\t\ti;\n\n\tif ('ref' in props) {\n\t\tref = props.ref;\n\t\tdelete props.ref;\n\t}\n\n\t/** @type {VNode & { __source: any; __self: any }} */\n\tconst vnode = {\n\t\ttype,\n\t\tprops: normalizedProps,\n\t\tkey,\n\t\tref,\n\t\t_children: null,\n\t\t_parent: null,\n\t\t_depth: 0,\n\t\t_dom: null,\n\t\t_nextDom: undefined,\n\t\t_component: null,\n\t\tconstructor: undefined,\n\t\t_original: --vnodeId,\n\t\t_index: -1,\n\t\t_flags: 0,\n\t\t__source,\n\t\t__self\n\t};\n\n\t// If a Component VNode, check for and apply defaultProps.\n\t// Note: `type` is often a String, and can be `undefined` in development.\n\tif (typeof type === 'function' && (ref = type.defaultProps)) {\n\t\tfor (i in ref)\n\t\t\tif (typeof normalizedProps[i] === 'undefined') {\n\t\t\t\tnormalizedProps[i] = ref[i];\n\t\t\t}\n\t}\n\n\tif (options.vnode) options.vnode(vnode);\n\treturn vnode;\n}\n\n/**\n * Create a template vnode. This function is not expected to be\n * used directly, but rather through a precompile JSX transform\n * @param {string[]} templates\n * @param  {Array<string | null | VNode>} exprs\n * @returns {VNode}\n */\nfunction jsxTemplate(templates, ...exprs) {\n\tconst vnode = createVNode(Fragment, { tpl: templates, exprs });\n\t// Bypass render to string top level Fragment optimization\n\tvnode.key = vnode._vnode;\n\treturn vnode;\n}\n\nconst JS_TO_CSS = {};\nconst CSS_REGEX = /[A-Z]/g;\n\n/**\n * Serialize an HTML attribute to a string. This function is not\n * expected to be used directly, but rather through a precompile\n * JSX transform\n * @param {string} name The attribute name\n * @param {*} value The attribute value\n * @returns {string}\n */\nfunction jsxAttr(name, value) {\n\tif (options.attr) {\n\t\tconst result = options.attr(name, value);\n\t\tif (typeof result === 'string') return result;\n\t}\n\n\tif (name === 'ref' || name === 'key') return '';\n\tif (name === 'style' && typeof value === 'object') {\n\t\tlet str = '';\n\t\tfor (let prop in value) {\n\t\t\tlet val = value[prop];\n\t\t\tif (val != null && val !== '') {\n\t\t\t\tconst name =\n\t\t\t\t\tprop[0] == '-'\n\t\t\t\t\t\t? prop\n\t\t\t\t\t\t: JS_TO_CSS[prop] ||\n\t\t\t\t\t\t\t(JS_TO_CSS[prop] = prop.replace(CSS_REGEX, '-$&').toLowerCase());\n\n\t\t\t\tlet suffix = ';';\n\t\t\t\tif (\n\t\t\t\t\ttypeof val === 'number' &&\n\t\t\t\t\t// Exclude custom-attributes\n\t\t\t\t\t!name.startsWith('--') &&\n\t\t\t\t\t!IS_NON_DIMENSIONAL.test(name)\n\t\t\t\t) {\n\t\t\t\t\tsuffix = 'px;';\n\t\t\t\t}\n\t\t\t\tstr = str + name + ':' + val + suffix;\n\t\t\t}\n\t\t}\n\t\treturn name + '=\"' + str + '\"';\n\t}\n\n\tif (\n\t\tvalue == null ||\n\t\tvalue === false ||\n\t\ttypeof value === 'function' ||\n\t\ttypeof value === 'object'\n\t) {\n\t\treturn '';\n\t} else if (value === true) return name;\n\n\treturn name + '=\"' + encodeEntities(value) + '\"';\n}\n\n/**\n * Escape a dynamic child passed to `jsxTemplate`. This function\n * is not expected to be used directly, but rather through a\n * precompile JSX transform\n * @param {*} value\n * @returns {string | null | VNode | Array<string | null | VNode>}\n */\nfunction jsxEscape(value) {\n\tif (\n\t\tvalue == null ||\n\t\ttypeof value === 'boolean' ||\n\t\ttypeof value === 'function'\n\t) {\n\t\treturn null;\n\t}\n\n\tif (typeof value === 'object') {\n\t\t// Check for VNode\n\t\tif (value.constructor === undefined) return value;\n\n\t\tif (isArray(value)) {\n\t\t\tfor (let i = 0; i < value.length; i++) {\n\t\t\t\tvalue[i] = jsxEscape(value[i]);\n\t\t\t}\n\t\t\treturn value;\n\t\t}\n\t}\n\n\treturn encodeEntities('' + value);\n}\n\nexport {\n\tcreateVNode as jsx,\n\tcreateVNode as jsxs,\n\tcreateVNode as jsxDEV,\n\tFragment,\n\t// precompiled JSX transform\n\tjsxTemplate,\n\tjsxAttr,\n\tjsxEscape\n};\n"], "names": ["ENCODED_ENTITIES", "encodeEntities", "str", "length", "test", "last", "i", "out", "ch", "charCodeAt", "slice", "IS_NON_DIMENSIONAL", "vnodeId", "isArray", "Array", "createVNode", "type", "props", "key", "isStaticChildren", "__source", "__self", "ref", "normalizedProps", "vnode", "__k", "__", "__b", "__e", "__d", "undefined", "__c", "constructor", "__v", "__i", "__u", "defaultProps", "options", "jsxTemplate", "templates", "Fragment", "tpl", "exprs", "call", "arguments", "JS_TO_CSS", "CSS_REGEX", "jsxAttr", "name", "value", "attr", "result", "prop", "val", "replace", "toLowerCase", "suffix", "startsWith", "jsxEscape"], "mappings": "4EAAA,IAAMA,EAAmB,QAGlB,SAASC,EAAeC,GAE9B,GAAmB,IAAfA,EAAIC,SAA+C,IAA/BH,EAAiBI,KAAKF,GAAgB,OAAOA,EAQrE,IANA,IAAIG,EAAO,EACVC,EAAI,EACJC,EAAM,GACNC,EAAK,GAGCF,EAAIJ,EAAIC,OAAQG,IAAK,CAC3B,OAAQJ,EAAIO,WAAWH,IACtB,KAAK,GACJE,EAAK,SACL,MACD,KAAO,GACNA,EAAK,QACL,MACD,KAAK,GACJA,EAAK,OACL,MACD,QACC,SAGEF,IAAMD,IAAME,GAAOL,EAAIQ,MAAML,EAAMC,IACvCC,GAAOC,EAEPH,EAAOC,EAAI,CACZ,CAEA,OADIA,IAAMD,IAAME,GAAOL,EAAIQ,MAAML,EAAMC,IAChCC,CACR,CCrBO,IAAMI,EACZ,oECXGC,EAAU,EAERC,EAAUC,MAAMD,QAsBtB,SAASE,EAAYC,EAAMC,EAAOC,EAAKC,EAAkBC,EAAUC,GAC7DJ,IAAOA,EAAQ,CAAA,GAIpB,IACCK,EACAhB,EAFGiB,EAAkBN,EAIlB,QAASA,IACZK,EAAML,EAAMK,WACLL,EAAMK,KAId,IAAME,EAAQ,CACbR,KAAAA,EACAC,MAAOM,EACPL,IAAAA,EACAI,IAAAA,EACAG,IAAW,KACXC,GAAS,KACTC,IAAQ,EACRC,IAAM,KACNC,SAAUC,EACVC,IAAY,KACZC,iBAAaF,EACbG,MAAarB,EACbsB,KAAS,EACTC,IAAQ,EACRf,SAAAA,EACAC,OAAAA,GAKD,GAAoB,mBAATL,IAAwBM,EAAMN,EAAKoB,cAC7C,IAAK9B,KAAKgB,OACyB,IAAvBC,EAAgBjB,KAC1BiB,EAAgBjB,GAAKgB,EAAIhB,IAK5B,OADI+B,EAAQb,OAAOa,EAAQb,MAAMA,GAC1BA,CACR,CASA,SAASc,EAAYC,GACpB,IAAMf,EAAQT,EAAYyB,EAAU,CAAEC,IAAKF,EAAWG,SAAKhC,MAAAiC,KAAAC,UAAC,KAG5D,OADApB,EAAMN,IAAMM,EAAKS,IACVT,CACR,CAEA,IAAMqB,EAAY,CAAA,EACZC,EAAY,SAUlB,SAASC,EAAQC,EAAMC,GACtB,GAAIZ,EAAQa,KAAM,CACjB,IAAMC,EAASd,EAAQa,KAAKF,EAAMC,GAClC,GAAsB,iBAAXE,EAAqB,OAAOA,CACxC,CAEA,GAAa,QAATH,GAA2B,QAATA,EAAgB,MAAO,GAC7C,GAAa,UAATA,GAAqC,iBAAVC,EAAoB,CAClD,IAAI/C,EAAM,GACV,IAAK,IAAIkD,KAAQH,EAAO,CACvB,IAAII,EAAMJ,EAAMG,GAChB,GAAW,MAAPC,GAAuB,KAARA,EAAY,CAC9B,IAAML,EACM,KAAXI,EAAK,GACFA,EACAP,EAAUO,KACVP,EAAUO,GAAQA,EAAKE,QAAQR,EAAW,OAAOS,eAEjDC,EAAS,IAEG,iBAARH,GAENL,EAAKS,WAAW,OAChB9C,EAAmBP,KAAK4C,KAEzBQ,EAAS,OAEVtD,EAAMA,EAAM8C,EAAO,IAAMK,EAAMG,CAChC,CACD,CACA,OAAOR,EAAO,KAAO9C,EAAM,GAC5B,CAEA,OACU,MAAT+C,IACU,IAAVA,GACiB,mBAAVA,GACU,iBAAVA,EAEA,IACa,IAAVA,EAAuBD,EAE3BA,EAAO,KAAO/C,EAAegD,GAAS,GAC9C,CASA,SAASS,EAAUT,GAClB,GACU,MAATA,GACiB,kBAAVA,GACU,mBAAVA,EAEP,OAAO,KAGR,GAAqB,iBAAVA,EAAoB,CAE9B,QAA0BnB,IAAtBmB,EAAMjB,YAA2B,OAAOiB,EAE5C,GAAIpC,EAAQoC,GAAQ,CACnB,IAAK,IAAI3C,EAAI,EAAGA,EAAI2C,EAAM9C,OAAQG,IACjC2C,EAAM3C,GAAKoD,EAAUT,EAAM3C,IAE5B,OAAO2C,CACR,CACD,CAEA,OAAOhD,EAAe,GAAKgD,EAC5B"}