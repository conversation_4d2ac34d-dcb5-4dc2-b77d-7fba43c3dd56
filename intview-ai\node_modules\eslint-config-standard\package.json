{"name": "eslint-config-standard", "description": "JavaScript Standard Style - ESLint Shareable Config", "version": "17.1.0", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://feross.org"}, "bugs": {"url": "https://github.com/standard/eslint-config-standard/issues"}, "engines": {"node": ">=12.0.0"}, "devDependencies": {"@types/eslint": "^8.4.1", "@types/tape": "^4.13.2", "eslint": "^8.13.0", "eslint-plugin-import": "^2.25.4", "eslint-plugin-n": "^16.0.0", "eslint-plugin-promise": "^6.0.0", "tape": "^5.5.2"}, "homepage": "https://github.com/standard/eslint-config-standard", "keywords": ["JavaScript Standard Style", "check", "checker", "code", "code checker", "code linter", "code standards", "code style", "enforce", "eslint", "eslintconfig", "hint", "jscs", "j<PERSON>t", "lint", "policy", "quality", "simple", "standard", "standard style", "style", "style checker", "style linter", "verify"], "license": "MIT", "main": "index.js", "peerDependencies": {"eslint": "^8.0.1", "eslint-plugin-import": "^2.25.2", "eslint-plugin-n": "^15.0.0 || ^16.0.0 ", "eslint-plugin-promise": "^6.0.0"}, "repository": {"type": "git", "url": "git://github.com/standard/eslint-config-standard.git"}, "scripts": {"lint": "eslint .", "test": "npm run lint && tape test/*.js"}, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}]}