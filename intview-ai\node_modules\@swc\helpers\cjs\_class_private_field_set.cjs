"use strict";

var _class_apply_descriptor_set = require("./_class_apply_descriptor_set.cjs");
var _class_extract_field_descriptor = require("./_class_extract_field_descriptor.cjs");

function _class_private_field_set(receiver, privateMap, value) {
    var descriptor = _class_extract_field_descriptor._(receiver, privateMap, "set");
    _class_apply_descriptor_set._(receiver, descriptor, value);
    return value;
}
exports._ = _class_private_field_set;
