/**
 * <AUTHOR> <https://github.com/mysticatea>
 * See LICENSE file in root directory for full license.
 */
"use strict"

module.exports = {
    meta: {
        docs: {
            description: "disallow class declarations.",
            category: "ES2015",
            recommended: false,
            url: "http://eslint-community.github.io/eslint-plugin-es-x/rules/no-classes.html",
        },
        fixable: null,
        messages: {
            forbidden: "ES2015 class declarations are forbidden.",
        },
        schema: [],
        type: "problem",
    },
    create(context) {
        return {
            "ClassDeclaration, ClassExpression"(node) {
                context.report({ node, messageId: "forbidden" })
            },
        }
    },
}
