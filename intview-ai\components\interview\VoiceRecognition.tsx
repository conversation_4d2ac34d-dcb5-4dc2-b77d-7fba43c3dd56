"use client";
import React, { useState, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Mic, Square } from "lucide-react";

// Import regenerator-runtime for async/await support
import "regenerator-runtime/runtime";

// Dynamic import to avoid SSR issues
let SpeechRecognition: any = null;
let useSpeechRecognition: any = null;

if (typeof window !== "undefined") {
  const speechRecognitionModule = require("react-speech-recognition");
  SpeechRecognition = speechRecognitionModule.default;
  useSpeechRecognition = speechRecognitionModule.useSpeechRecognition;
}

interface VoiceRecognitionProps {
  onTranscriptChange: (transcript: string) => void;
  onFinalTranscript: (transcript: string) => void;
  isDisabled?: boolean;
  className?: string;
}

const VoiceRecognition: React.FC<VoiceRecognitionProps> = ({
  onTranscriptChange,
  onFinalTranscript,
  isDisabled = false,
  className = "",
}) => {
  const [isRecording, setIsRecording] = useState(false);
  const [hasStarted, setHasStarted] = useState(false);
  const [isClient, setIsClient] = useState(false);

  // Ensure we're on the client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Initialize speech recognition hooks only on client side
  const speechRecognitionData = isClient && useSpeechRecognition ? useSpeechRecognition() : {
    transcript: "",
    listening: false,
    resetTranscript: () => {},
    browserSupportsSpeechRecognition: false,
    isMicrophoneAvailable: false,
  };

  const {
    transcript,
    listening,
    resetTranscript,
    browserSupportsSpeechRecognition,
    isMicrophoneAvailable,
  } = speechRecognitionData;

  // Update parent component with transcript changes
  useEffect(() => {
    if (transcript) {
      onTranscriptChange(transcript);
    }
  }, [transcript, onTranscriptChange]);

  // Handle when speech recognition stops
  useEffect(() => {
    if (hasStarted && !listening && transcript) {
      onFinalTranscript(transcript);
      setIsRecording(false);
      setHasStarted(false);
    }
  }, [listening, transcript, hasStarted, onFinalTranscript]);

  const startListening = () => {
    if (!browserSupportsSpeechRecognition || !isMicrophoneAvailable || isDisabled) {
      return;
    }

    resetTranscript();
    setIsRecording(true);
    setHasStarted(true);
    
    SpeechRecognition.startListening({
      continuous: true,
      language: "en-US",
    });
  };

  const stopListening = () => {
    SpeechRecognition.stopListening();
    setIsRecording(false);
    
    // Trigger final transcript if we have content
    if (transcript) {
      onFinalTranscript(transcript);
    }
    setHasStarted(false);
  };

  const clearTranscript = () => {
    resetTranscript();
    onTranscriptChange("");
  };

  if (!browserSupportsSpeechRecognition) {
    return (
      <div className={`bg-red-50 border border-red-200 rounded-lg p-4 ${className}`}>
        <p className="text-red-700 text-sm">
          Your browser doesn't support speech recognition. Please use a modern browser like Chrome, Edge, or Safari.
        </p>
      </div>
    );
  }

  if (!isMicrophoneAvailable) {
    return (
      <div className={`bg-yellow-50 border border-yellow-200 rounded-lg p-4 ${className}`}>
        <p className="text-yellow-700 text-sm">
          Microphone access is required for voice input. Please allow microphone permissions and refresh the page.
        </p>
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Voice Controls */}
      <div className="flex items-center gap-3">
        {!isRecording ? (
          <Button
            onClick={startListening}
            disabled={isDisabled}
            variant="default"
            size="sm"
            className="flex items-center gap-2 bg-blue-600 hover:bg-blue-700"
          >
            <Mic className="w-4 h-4" />
            Start Recording
          </Button>
        ) : (
          <Button
            onClick={stopListening}
            variant="destructive"
            size="sm"
            className="flex items-center gap-2"
          >
            <Square className="w-4 h-4" />
            Stop Recording
          </Button>
        )}

        {transcript && (
          <Button
            onClick={clearTranscript}
            variant="outline"
            size="sm"
            className="flex items-center gap-2"
          >
            Clear
          </Button>
        )}
      </div>

      {/* Recording Status */}
      {isRecording && (
        <div className="flex items-center gap-2 text-red-600">
          <div className="w-3 h-3 bg-red-600 rounded-full animate-pulse"></div>
          <span className="text-sm font-medium">Recording... Speak now</span>
        </div>
      )}

      {/* Live Transcript Display */}
      {transcript && (
        <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h4 className="text-sm font-medium text-gray-700 mb-2">
            {isRecording ? "Live Transcript:" : "Recorded Answer:"}
          </h4>
          <p className="text-gray-800 text-sm leading-relaxed">
            {transcript}
          </p>
        </div>
      )}

      {/* Instructions */}
      <div className="text-xs text-gray-500">
        <p>💡 Click "Start Recording" and speak your answer clearly. Click "Stop Recording" when finished.</p>
      </div>
    </div>
  );
};

export default VoiceRecognition;
