"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  DefaultValues,
  FieldValues,
  Path,
  SubmitHandler,
  useForm,
} from "react-hook-form";
import { z, ZodType } from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import ROUTES from "@/constants/routes";
import Image from "next/image";

export interface AuthFormProps<T extends FieldValues> {
  schema: ZodType<T, T>;
  defaultValues: T;
  onSubmit: (data: T) => Promise<{ success: boolean }>;
  formType: "SIGN_IN" | "SIGN_UP" | "RESET_PASSWORD";
  heading: string;
  placeholderValues: { [key: string]: string };
}

const AuthForm = <T extends FieldValues>({
  schema,
  defaultValues,
  formType,
  heading,
  placeholderValues,
}: AuthFormProps<T>) => {
  const router = useRouter();
  const form = useForm<z.infer<typeof schema>>({
    resolver: zodResolver(schema),
    defaultValues: defaultValues as DefaultValues<T>,
    mode: "onChange", // ensures isValid updates live
  });

  const [focusedField, setFocusedField] = useState<string | null>(null);
  const [enableSubmitButton, setEnableSubmitButton] = useState(false);
  const [showPassword, setShowPassword] = useState(false); // State to toggle password visibility
  const [isLoading, setIsLoading] = useState(false);

  const handleSubmit: SubmitHandler<T> = async (data) => {
    setIsLoading(true);

    try {
      // Simulate API call delay
      await new Promise(resolve => setTimeout(resolve, 1000));


      // Show success toast
      const successMessage = formType === "SIGN_IN"
        ? "Successfully signed in! Welcome back."
        : "Account created successfully! Welcome to AI Interview.";

      toast.success(successMessage);
      setTimeout(() => {
        router.push(ROUTES.HOME);
      }, 1000);

    } catch (error) {
      console.error("Authentication error:", error);

      // Show error toast
      const errorMessage = formType === "SIGN_IN"
        ? "Sign in failed. Please check your credentials."
        : "Account creation failed. Please try again.";

      toast.error(errorMessage);
    } finally {
      setIsLoading(false);
    }
  };

  let buttonText = "Continue";
  if (formType === "SIGN_IN") buttonText = "Sign In";
  else if (formType === "SIGN_UP") buttonText = "Create an Account";
  else if (formType === "RESET_PASSWORD") buttonText = "Reset Password";

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className="space-y-4 pt-5 px-0 w-full"
      >
        <h1 className="text-[34px] leading-[41px] font-semibold font-poppins text-gray-900 mb-8">
          {heading}
        </h1>

        {Object.keys(defaultValues).map((fieldName) => (
          <FormField
            key={fieldName}
            control={form.control}
            name={fieldName as Path<T>}
            render={({ field }) => {
              const error = form.formState.errors[field.name];
              const isTouched = form.getFieldState(field.name).isTouched;
              const isFocused = focusedField === field.name;
              const isValid = isTouched && !error;

              const borderClass = isFocused ? " " : isValid ? "" : "";

              return (
                <FormItem className="flex w-full flex-col gap-3.5">
                  <FormLabel className="paragraph-medium text-dark400_light700">
                    {field.name === "email"
                      ? "Email Address"
                      : field.name.charAt(0).toUpperCase() +
                        field.name.slice(1)}
                  </FormLabel>
                  <FormControl>
                    <div className="relative w-full">
                      <Input
                        type={
                          field.name.toLowerCase().includes("password") &&
                          !showPassword
                            ? "password"
                            : "text"
                        }
                        {...field}
                        onFocus={() => setFocusedField(field.name)}
                        onBlur={() => setFocusedField(null)}
                        className={`paragraph-regular background-light900_dark300 text-dark300_light700 min-h-12 rounded-1.5 border focus:outline-none w-full ${borderClass}`}
                        placeholder={placeholderValues[field.name] || ""}
                      />
                      {field.name.toLowerCase().includes("password") && (
                        <button
                          type="button"
                          onClick={() => setShowPassword(!showPassword)}
                          className="absolute right-4 top-1/2 transform -translate-y-1/2 hover:cursor-pointer"
                        >
                          <Image
                            src="/images/eye.png" // Replace this with your eye icon path
                            alt="Toggle Password Visibility"
                            width={20} // Adjust size as needed
                            height={20} // Adjust size as needed
                          />
                        </button>
                      )}
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              );
            }}
          />
        ))}

        {formType === "SIGN_IN" && (
          <div className="flex justify-end mt-5">
            <Link
              href={"/sign-in/Forgotpassword"}
              className="font-medium underline text-[#7B61FF] mb-14"
            >
              Forgot Password?
            </Link>
          </div>
        )}

        {formType === "SIGN_UP" && (
          <div className="flex items-start gap-3 text-sm">
            <input
              type="checkbox"
              id="terms"
              className="mt-1"
              onChange={(e) => setEnableSubmitButton(e.target.checked)}
            />
            <label htmlFor="terms" className="text-gray-700">
              I agree to all the{" "}
              <span className="text-[#6938EF] cursor-pointer">Terms</span> of
              service and{" "}
              <span className="text-[#6938EF] cursor-pointer">Privacy</span>{" "}
              <span className="text-[#6938EF] cursor-pointer">Policies</span>
            </label>
          </div>
        )}
        <div className="flex justify-center w-full">
          <Button
            className="primary-button paragraph-medium w-full max-w-[370px] min-h-12 rounded-4xl px-4 py-3 font-inter !text-light-900 text-white hover:cursor-pointer disabled:opacity-50 disabled:cursor-not-allowed"
            disabled={
              isLoading ||
              form.formState.isSubmitting ||
              !form.formState.isValid ||
              (formType === "SIGN_UP" && !enableSubmitButton)
            }
          >
            {isLoading || form.formState.isSubmitting
              ? formType === "SIGN_IN"
                ? "Signing In..."
                : formType === "SIGN_UP"
                ? "Creating Account..."
                : "Processing..."
              : buttonText}
          </Button>
        </div>

        {formType === "SIGN_IN" ? (
          <p className="text-center mb-14">
            {"Don't have an account? "}
            <Link
              href={ROUTES.SIGN_UP}
              className=" font-semibold underline text-[#7B61FF]"
            >
              Sign Up
            </Link>
          </p>
        ) : (
          <p className="text-center">
            Already have an account?{" "}
            <Link
              href={ROUTES.SIGN_IN}
              className="underline text-[#6938EF] font-medium "
            >
              Login
            </Link>
          </p>
        )}

        <div
          className={`flex items-center justify-around flex-col gap-2 sm:flex-row sm:gap-0${
            formType === "SIGN_IN" ? "mt-16" : "mt-16"
          }`}
        >
          <span className="text-md text-[#000000]">Or continue with</span>
          <div className="flex items-center gap-2">
            <button
              type="button"
              className="border rounded-full hover:bg-gray-700 hover:cursor-pointer transition"
              aria-label="Continue with Google"
            >
              <Image
                src="/icons/google.svg"
                alt="Google"
                width={32}
                height={32}
              />
            </button>
            <button
              type="button"
              className="border rounded-full hover:bg-gray-700 hover:cursor-pointer transition"
              aria-label="Continue with Facebook"
            >
              <Image
                src="/icons/facebook.svg"
                alt="Facebook"
                width={32}
                height={32}
              />
            </button>
            <button
              type="button"
              className="border rounded-full  transition hover:bg-gray-700 hover:cursor-pointer"
              aria-label="Continue with Apple"
            >
              <Image
                src="/icons/apple.svg"
                alt="Apple"
                width={32}
                height={32}
              />
            </button>
          </div>
        </div>
      </form>
    </Form>
  );
};

export default AuthForm;
