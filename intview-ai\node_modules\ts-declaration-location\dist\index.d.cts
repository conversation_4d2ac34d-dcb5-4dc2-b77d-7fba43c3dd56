// Generated by dts-bundle-generator v9.5.1

import ts from 'typescript';

/**
 * How a type declaration can be matched.
 */
export type TypeDeclarationSpecifier = TypeDeclarationFileSpecifier | TypeDeclarationLibSpecifier | TypeDeclarationPackageSpecifier;
/**
 * Type declaration defined in a file.
 */
export type TypeDeclarationFileSpecifier = {
	from: "file";
	/**
	 * The path to look in for the type, relative to project directory.
	 */
	path?: string;
};
/**
 * Type declaration defined in the ts lib.
 */
export type TypeDeclarationLibSpecifier = {
	from: "lib";
};
/**
 * Type declaration defined in a package.
 */
export type TypeDeclarationPackageSpecifier = {
	from: "package";
	/**
	 * The package to look in.
	 */
	package?: string;
};
/**
 * Test if the type matches the given specifier.
 */
declare function typeMatchesSpecifier(program: ts.Program, specifier: Readonly<TypeDeclarationSpecifier>, type: Readonly<ts.Type>): boolean;

export {
	typeMatchesSpecifier as default,
};

export {};
