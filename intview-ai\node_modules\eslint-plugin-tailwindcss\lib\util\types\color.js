const cssNamedColors = [
  'indianred',
  'lightcoral',
  'salmon',
  'darksalmon',
  'lightsalmon',
  'crimson',
  'red',
  'firebrick',
  'darkred',
  'pink',
  'lightpink',
  'hotpink',
  'deeppink',
  'mediumvioletred',
  'palevioletred',
  'coral',
  'tomato',
  'orangered',
  'darkorange',
  'orange',
  'gold',
  'yellow',
  'lightyellow',
  'lemonchiffon',
  'lightgoldenrodyellow',
  'papayawhip',
  'moccasin',
  'peachpuff',
  'palegoldenrod',
  'khaki',
  'darkkhaki',
  'lavender',
  'thistle',
  'plum',
  'violet',
  'orchid',
  'fuchsia',
  'magenta',
  'mediumorchid',
  'mediumpurple',
  'blueviolet',
  'darkviolet',
  'darkorchid',
  'darkmagenta',
  'purple',
  'rebeccapurple',
  'indigo',
  'mediumslateblue',
  'slateblue',
  'darkslateblue',
  'greenyellow',
  'chartreuse',
  'lawngreen',
  'lime',
  'limegreen',
  'palegreen',
  'lightgreen',
  'mediumspringgreen',
  'springgreen',
  'mediumseagreen',
  'seagreen',
  'forestgreen',
  'green',
  'darkgreen',
  'yellowgreen',
  'olivedrab',
  'olive',
  'darkolivegreen',
  'mediumaquamarine',
  'darkseagreen',
  'lightseagreen',
  'darkcyan',
  'teal',
  'aqua',
  'cyan',
  'lightcyan',
  'paleturquoise',
  'aquamarine',
  'turquoise',
  'mediumturquoise',
  'darkturquoise',
  'cadetblue',
  'steelblue',
  'lightsteelblue',
  'powderblue',
  'lightblue',
  'skyblue',
  'lightskyblue',
  'deepskyblue',
  'dodgerblue',
  'cornflowerblue',
  'royalblue',
  'blue',
  'mediumblue',
  'darkblue',
  'navy',
  'midnightblue',
  'cornsilk',
  'blanchedalmond',
  'bisque',
  'navajowhite',
  'wheat',
  'burlywood',
  'tan',
  'rosybrown',
  'sandybrown',
  'goldenrod',
  'darkgoldenrod',
  'peru',
  'chocolate',
  'saddlebrown',
  'sienna',
  'brown',
  'maroon',
  'white',
  'snow',
  'honeydew',
  'mintcream',
  'azure',
  'aliceblue',
  'ghostwhite',
  'whitesmoke',
  'seashell',
  'beige',
  'oldlace',
  'floralwhite',
  'ivory',
  'antiquewhite',
  'linen',
  'lavenderblush',
  'mistyrose',
  'gainsboro',
  'lightgray',
  'lightgrey',
  'silver',
  'darkgray',
  'darkgrey',
  'gray',
  'grey',
  'dimgray',
  'dimgrey',
  'lightslategray',
  'lightslategrey',
  'slategray',
  'slategrey',
  'darkslategray',
  'darkslategrey',
  'black',
  'transparent',
  'currentColor',
];

// RGB[A] hexa: #123456AA, #B4DA55, #000A, #123
const hexRGBA = '\\#(([0-9A-Fa-f]{8})|([0-9A-Fa-f]{6})|([0-9A-Fa-f]{4})|([0-9A-Fa-f]{3}))';

// RGB 0-255: rgb(10,20,30)
const RGBIntegers = 'rgb\\(\\d{1,3}\\,\\d{1,3}\\,\\d{1,3}\\)';

// RGB %: rgb(25%,50%,75%)
const RGBPercentages = 'rgb\\(\\d{1,3}%\\,\\d{1,3}%\\,\\d{1,3}%\\)';

// RGBA: rgba(50,100,255,.5), rgba(50,100,255,50%)
const supportedRGBA = 'rgba\\(\\d{1,3}\\,\\d{1,3}\\,\\d{1,3}\\,\\d*(\\.\\d*)?%?\\)';

const RGBAPercentages = 'rgba\\(\\d{1,3}%\\,\\d{1,3}%\\,\\d{1,3}%\\,\\d*(\\.\\d*)?%?\\)';

const optionalColorPrefixedVar = '(color\\:)?var\\(\\-\\-[A-Za-z\\-]{1,}\\)';

const mandatoryColorPrefixed = 'color\\:(?!(hsla\\()).{1,}';

const notHSLAPlusWildcard = '(?!(hsla\\()).{1,}';

// HSL
const supportedHSL = 'hsl\\(\\d{1,3}%?\\,\\d{1,3}%?\\,\\d{1,3}%?\\)';

// 'hsla\\(\\d{1,3}%?\\,\\d{1,3}%?\\,\\d{1,3}%?\\,\\d*(\\.\\d*)?%?\\)',

const colorValues = [hexRGBA, RGBIntegers, RGBPercentages, supportedRGBA, supportedHSL];

const mergedColorValues = [...cssNamedColors, ...colorValues];

module.exports = {
  cssNamedColors,
  colorValues,
  mergedColorValues,
  RGBAPercentages,
  optionalColorPrefixedVar,
  mandatoryColorPrefixed,
  notHSLAPlusWildcard,
};
