{"version": 3, "middleware": {"/": {"files": ["server/edge/chunks/_5399b416._.js", "server/edge/chunks/node_modules_@auth_core_5ebafa38._.js", "server/edge/chunks/node_modules_jose_dist_webapi_49ff121e._.js", "server/edge/chunks/node_modules_e184ff1b._.js", "server/edge/chunks/[root-of-the-server]__df53d061._.js", "server/edge/chunks/edge-wrapper_3d09a47d.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "^/.*$", "originalSource": "/:path*"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "9R+Wv7DZZYhX4pQr08UnGwtOukSaA3NFvRrMYN48HJE=", "__NEXT_PREVIEW_MODE_ID": "97ab7b81bac6da1c3316b0c4e8194426", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "bcd3d2ee557aeba361067e1c31a5a8f88e1f8595d662f582630df01eaf9015ae", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "be94076576e303770afcc1194797d5c7d1de204a2fa183c39094e895e24c458f"}}}, "sortedMiddleware": ["/"], "functions": {}}